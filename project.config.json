{"appid": "wx17d823d73e4f958c", "compileType": "miniprogram", "libVersion": "3.7.8", "packOptions": {"ignore": [], "include": []}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "condition": false}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}