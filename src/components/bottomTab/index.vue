<template>
    <view class="bottom-tab">
        <view class="menu-list">
            <view
                v-for="(item, index) in tabList"
                class="list-item"
                :class="{'active': currentIndex === index}"
                @click="handlePush(item, index)"
                :key="index"
            >
                <view class="list-item__text">
                    {{ item.text }}
                </view>
                <view class="underline" v-if="currentIndex === index">——</view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    props: {
        currentIndex: { // 当前选中的tab项
            type: Number,
            default: 0,
        },
    },
    data() {
        return {
            tabList: [{
                'text': '首页',
                'pagePath': 'pages/home/<USER>',
            }, {
                'text': '资源',
                'pagePath': 'pages/resource/index',
            }, {
                'text': '排料',
                'pagePath': 'pages/plList/index',
            }, {
                'text': '裁切',
                'pagePath': 'pages/cut/index',
            }, {
                'text': '我的',
                'pagePath': 'pages/account/index',
            }],
            showTabBar: false,
        };
    },
    methods: {
        handlePush(item, index) {
            if ([1, 3].includes(index)) {
                uni.showToast({
                    title: '正在筹备中，敬请期待',
                    icon: 'none',
                });
                return;
            }
            if (index === this.currentIndex) {
                return;
            }
            uni.switchTab({
                url: this.tabList[index].pagePath,
            });
        },
    },
};
</script>

<style lang="scss">
.bottom-tab {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100vw;
    height: 70px;
    z-index: 999;
    background: #000;
    .menu-list {
        display: flex;
        justify-content: space-between;
        height: 70px;
        box-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
        .list-item {
            width: 20vw;
            padding-top: 15px;
            &__text {
                color: #fff;
                height: 20px;
                line-height: 20px;
                font-size: 18px;
                text-align: center;
                font-weight: normal;
            }
            .underline {
                display: none;
            }
            &.active {
                .list-item__text {
                    font-weight: bold;
                }
                .underline {
                    display: block;
                    color: #fff;
                    height: 5px;
                    padding-left: 22px;
                    line-height: 10px;
                    font-weight: 900;
                }
            }
        }
    }
}
</style>
