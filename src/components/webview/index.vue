
<template>
    <div>
        <web-view :src="url" @message="handleMessage"></web-view>
    </div>
</template>
<script>
export default {
    props: {
        url: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            webviewPath: '',
        };
    },
    methods: {
        handleMessage(msgData) {
            console.log('webview message', msgData);
        },
    },
};
</script>
