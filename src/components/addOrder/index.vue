<template>
    <view class="order-form">
        <uni-forms
            :modelValue="formData"
            style="padding-bottom: 90px;"
            label-position="top"
            label-width="150"
        >
            <view class="form-section">
                <uni-forms-item label="任务名称：" name="orderName" required>
                    <uni-easyinput
                        type="text"
                        maxlength="15"
                        @change="resetSubmitBtnStatus"
                        v-model="formData.orderName"
                        placeholder="请输入任务名称"
                        :disabled="readonly"
                    />
                </uni-forms-item>
                <uni-forms-item label="客户名称：" name="customerName" :label-style="{fontSize: '16px'}">
                    <uni-easyinput
                        maxlength="15"
                        type="text"
                        @change="resetSubmitBtnStatus"
                        v-model="formData.customerName"
                        placeholder="请输入客户名称"
                        :disabled="readonly"
                    />
                </uni-forms-item>
            </view>
            <view class="form-section">
                <uni-forms-item class="double-input" name="priority" label="优先排版：">
                    <view class="config-row">
                        <radio
                            @click="handleRadioClick('1')"
                            class="config-row__radio"
                            value="1"
                            :checked="priority === '1'"
                            color="#02A7F0"
                            :disabled="readonly"
                        />
                        <text>大裁片</text><text class="config-row__gap">≥</text>
                        <uni-easyinput
                            type="number"
                            v-model="formData.bigCpArea"
                            @change="resetSubmitBtnStatus"
                            :disabled="readonly"
                        />
                        （ft²）
                    </view>
                    <view class="single-cp config-row">
                        <radio
                            @click="handleRadioClick('2')"
                            class="config-row__radio"
                            value="2"
                            color="#02A7F0"
                            :checked="priority === '2'"
                            :disabled="readonly"
                        />
                        <text>启用单块裁片优先级的设置</text>
                    </view>
                </uni-forms-item>
            </view>
            <view class="form-section">
                <uni-forms-item class="double-input" name="priority" label="间距设置：">
                    <view class="config-row border-config">
                        <text class="row-title">皮料边缘剩余</text>
                        <uni-easyinput
                            type="number"
                            v-model="formData.plEdgeSurplus"
                            @change="resetSubmitBtnStatus"
                            :disabled="readonly"
                        />
                        <span class="unit-sub">（毫米）</span>
                    </view>
                    <view class="cp-gap config-row">
                        <text class="row-title">裁片间距</text>
                        <uni-easyinput
                            type="number"
                            v-model="formData.cpSpacing"
                            @change="resetSubmitBtnStatus"
                            :disabled="readonly"
                        />
                        <span class="unit-sub">（毫米）</span>
                    </view>
                </uni-forms-item>
            </view>
            <view class="form-section">
                <uni-forms-item required name="mostTime" label="排版时间：">
                    <view class="config-row time-conifg">
                        <text class="time-label">每张皮料排版时间</text>
                        <uni-easyinput
                            class="test"
                            type="number"
                            v-model="formData.mostTime"
                            @change="resetSubmitBtnStatus"
                            :disabled="readonly"
                        />（秒）
                    </view>
                </uni-forms-item>
            </view>
            <view class="form-section single-config">
                <view class="config-row">
                    <template v-if="!readonly">
                        <checkbox
                            @click="handleCheckboxSelect"
                            class="config-row__radio"
                            :checked="formData.isSmallCp"
                            color="#02A7F0"
                            :disabled="readonly"
                        />
                    </template>
                    <span v-else class="readonly-text">{{ formData.isSmallCp ? '是' : '否' }}</span>
                    <text class="config-row__text">小裁片分布到所有皮料 (避免皮料边缘)</text><text
                        class="config-row__gap"
                    >≤</text>
                    <uni-easyinput
                        type="number"
                        v-model="formData.smallCpArea"
                        @change="resetSubmitBtnStatus"
                        :disabled="readonly"
                    />
                    （ft²）
                </view>
            </view>
            <view class="form-section no-border">
                <uni-forms-item name="remark" label="备注：">
                    <uni-easyinput
                        type="textarea"
                        v-model="formData.remark"
                        placeholder="请输入备注"
                        :disabled="readonly"
                    />
                </uni-forms-item>
            </view>
            <view class="item-for-gap"></view>
        </uni-forms>
        <div class="submit-footer" v-if="!readonly">
            <button
                :class="saveDisabled ? 'disabled' : ''"
                :disabled="saveDisabled"
                class="submit-btn btn-primary"
                @click="handleSubmit"
            >
                保存任务
            </button>
        </div>
    </view>
</template>
<script>
import { saveTask, configTask } from 'api/order';

export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        orderId: {
            type: String,
            default: '',
        },
        initData: {
            type: Object,
            default: () => ({}),
        },
        readonly: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            priority: '',
            formData: {
                orderName: '',
                customerName: '',
                isSinglePriority: 0,
                isBigCp: 0,
                bigCpArea: '',
                mostTime: '60',
                isSmallCp: 0,
                smallCpArea: '',
                remark: '',
                cpSpacing: '',
                plEdgeSurplus: '',
            },
            cacheOrderInfo: {},
            saveDisabled: false,
        };
    },
    watch: {
        visible(val) {
            if (val && !this.orderId) {
                this.formData = {
                    orderName: '',
                    customerName: '',
                    isSinglePriority: 0,
                    isBigCp: 0,
                    bigCpArea: '',
                    mostTime: '60',
                    isSmallCp: 0,
                    smallCpArea: '',
                    remark: '',
                    cpSpacing: '',
                    plEdgeSurplus: '',
                };
            }
        },
        initData: {
            handler(val) {
                if (val) {
                    this.formData = {
                        orderName: val.orderName,
                        customerName: val.customerName,
                        isSinglePriority: val.isSinglePriority,
                        isBigCp: val.isBigCp,
                        bigCpArea: val.bigCpArea,
                        mostTime: val.mostTime,
                        isSmallCp: val.isSmallCp,
                        smallCpArea: val.smallCpArea,
                        remark: val.remark,
                        cpSpacing: val.cpSpacing,
                        plEdgeSurplus: val.plEdgeSurplus,
                    };
                    this.cacheOrderInfo = { ...this.formData };
                    this.priority = val.isSinglePriority ? '2' : val.isBigCp ? '1' : '';
                    this.saveDisabled = true;
                }
            },
            immediate: true,
        },
    },
    methods: {
        resetSubmitBtnStatus() {
            this.saveDisabled = false;
        },
        handleSubmit() {
            if (!this.formData.orderName) {
                this.$toast.error('请输入任务名称');
                return;
            }
            if (!this.formData.mostTime) {
                this.$toast.error('请输入排版时间');
                return;
            }
            if (this.priority === '1' && !this.formData.bigCpArea) {
                this.$toast.error('请输入大裁片面积');
                return;
            }

            if (this.formData.isSmallCp && !this.formData.smallCpArea) {
                this.$toast.error('请输入小裁片分布到所有皮料面积上限');
                return;
            }
            const params = {
                ...this.formData,
                isSinglePriority: Number(this.priority === '2'),
                isBigCp: Number(this.priority === '1'),
                isSmallCp: Number(this.formData.isSmallCp),
            };
            const apiFun = !this.orderId ? saveTask : configTask;
            if (this.orderId) {
                params.id = +this.orderId;
            }
            apiFun(params).then(({ data }) => {
                if (data.code === 0) {
                    this.saveDisabled = true;
                    this.$toast.success('保存成功');
                    this.cacheOrderInfo = { ...this.params };
                    if (data.data) {
                        this.$emit('updateOrderId', data.data);
                    }
                    this.$emit('updateInfo');
                } else {
                    this.$toast.error('保存失败');
                }
            }).catch(() => {
                this.$toast.error('保存失败');
            });
        },
        handleCheckboxSelect() {
            if (this.readonly) {
                return;
            }
            this.resetSubmitBtnStatus();
            this.formData.isSmallCp = !this.formData.isSmallCp;
        },
        handleRadioClick(value) {
            if (this.readonly) {
                return;
            }
            this.resetSubmitBtnStatus();
            if (this.priority === value) {
                this.priority = '';
            } else {
                this.priority = value;
                if (value === '2') {
                    this.formData.bigCpArea = '';
                }
            }
        },
    },
};
</script>
<style lang="scss">
.order-form {
    height: 100%;
    padding: 20rpx 0 60rpx;
    overflow: auto;
    .label-form {
        height: calc(100% - 60rpx);
        padding-bottom: 90px;
    }

    .form-section {
        padding: 20rpx 40rpx;
        border-bottom: 1px solid #D9D9D9;
        &>view {
            margin-bottom: 10px;
        }
        text:not(.is-required), input, textarea {
            font-size: 16px !important;
            color: #000;
        }
        .uni-forms-item {

            margin-bottom: 10px;

            &__label {
                font-weight: bold;
                color: #000;
                font-size: 32 rpx;
                padding-bottom: 0 !important;
            }
        }

        &.single-config {
            padding: 20rpx 40rpx;
            font-size: 28rpx;
        }

        &.no-border {
            border: none;
        }
    }
    .config-row {
        display: flex;
        align-items: center;
        font-size: 32rpx;

        &__radio {
            transform: scale(0.7);
        }

        &__gap {
            padding: 0 10rpx;
        }

        &__text {
            width: 280rpx;
        }

        .readonly-text {
            color: #000;
            font-size: 28rpx;
            margin-right: 10rpx;
        }

        &.time-conifg {
            .time-label {
                padding-right: 18rpx;
            }
            view:first-child {
                padding-right: 7rpx;
            }
        }
        .row-title {
            width: 200rpx;
            text-align: right;
            padding-right: 20rpx;
        }
        &.border-config {
            margin-bottom: 20rpx;
        }
    }

    .single-cp {
        margin-top: 12rpx;
    }

    .item-for-gap {
        height: 80px;
        opacity: 0;
    }
    .submit-footer {
        position: fixed;
        bottom: 0;
        background: #fff;
        text-align: center;
        padding: 10px 20px;
        width: 100%;
        display: flex;
        justify-content: center;
        box-sizing: border-box;
        border-top: 1px solid #D9D9D9;
        z-index: 99;

        .submit-btn {
            margin: 0;
            background: red;
            color: #FFF;
            font-weight: 700;
            &.disabled {
                background: none;
            }
        }
    }
}
</style>
