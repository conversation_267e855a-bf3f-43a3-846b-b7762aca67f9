<template>
    <view class="leather-list">
        <view class="common-table">
            <view class="common-table__header">
                <view class="th-item order">编号</view>
                <view class="th-item flex">已选择的皮料</view>
                <view class="th-item color">色调</view>
                <view class="th-item level">分级</view>
                <view class="th-item line">布纹线</view>
                <view class="th-item opt">操作</view>
            </view>
            <view class="common-table__body">
                <template v-if="tableData.length">
                    <uni-swipe-action ref="swipeGroup" class="swipe-row" :auto-close="true">
                        <uni-swipe-action-item
                            v-for="(item, index) in tableData"
                            :key="item.id"
                            class="swipe-item"
                            @click="bindClick($event, item, index)"
                        >
                            <view class="row-item"
                                :class="{'light-bg':
                                    index%2}"
                            >
                                <view class="row-item__column order">{{ index + 1 }}</view>
                                <view class="row-item__column flex">
                                    <view class="info-name">{{ item.batchName }}</view>
                                    <view class="info-detail area-info">
                                        <text>{{ item.area || '0' }} ft²</text>
                                    </view>
                                </view>
                                <view class="row-item__column color">{{ item.color || '-' }}</view>
                                <view class="row-item__column level">
                                    <view>{{ item.plLevel }}</view>
                                    <view>{{ item.plLevelRate }}%</view>
                                </view>
                                <view class="row-item__column line">{{ item.textureLineAngle || '-' }}</view>
                                <view class="row-item__column opt">···</view>
                            </view>
                            <template v-slot:right>
                                <view class="right-opt">
                                    <text class="delete" @click="bindClick('delete', item, index)">删除</text>
                                    <view class="up opt-item" @click="bindClick('up', item, index)">
                                        <image class="opt-img" src="/static/arrow-up.png"></image>
                                    </view>
                                    <view class="down opt-item">
                                        <image @click="bindClick('down', item, index)" class="opt-img" src="/static/arrow-down.png"></image>
                                    </view>
                                    <view class="top opt-item" @click="bindClick('top', item, index)">
                                        <image class="opt-img" src="/static/arrow-top.png"></image>
                                    </view>
                                    <view class="bottom opt-item" @click="bindClick('bottom', item, index)">
                                        <image class="opt-img" src="/static/arrow-bottom.png"></image>
                                    </view>
                                </view>
                            </template>
                        </uni-swipe-action-item>
                    </uni-swipe-action>
                </template>
                <view class="no-data" v-else>
                    暂无数据
                </view>
            </view>
        </view>
    </view>
</template>

<script>

import { deleteLeather, orderLeatherSelectedData } from 'api/order';

export default {
    props: {
        orderId: {
            type: String,
            default: '',
        },
        tableData: {
            type: Array,
            default: () => ([]),
        },
    },
    data() {
        return {
            loading: false,
            rightOptions: [{
                text: '✖️',
                style: {
                    backgroundColor: '#F56C6C',
                },
            }, {
                text: '⏫️',
                style: {
                    backgroundColor: '#000',
                },
            }, {
                text: '⬆️',
                style: {
                    backgroundColor: '#000',
                },
            }, {
                text: '⏬',
                style: {
                    backgroundColor: '#000',
                },
            }, {
                text: '⬇️',
                style: {
                    backgroundColor: '#F56C6C',
                },
            }],
        };
    },
    methods: {
        closeAllSwipes() {
            try {
                if (this.$refs.swipeGroup && this.$refs.swipeGroup.closeAll) {
                    this.$refs.swipeGroup.closeAll();
                }
            } catch (e) {
                // noop
            }
        },
        bindClick(oI, item, index) {
            switch (oI) {
                case 'top':
                    this.moveItem(index, 'top');
                    break;
                case 'up':
                    this.moveItem(index, 'up');
                    break;
                case 'bottom':
                    this.moveItem(index, 'bottom');
                    break;
                case 'down':
                    this.moveItem(index, 'down');
                    break;
                default:
                    this.deleteItem(index);
                    break;
            }
            this.closeAllSwipes();
        },
        moveItem(index, direction) {
            const newData = [...this.tableData];
            if (direction === 'top') {
                newData.unshift(newData.splice(index, 1)[0]);
            } else if (direction === 'up') {
                if (index === 0) {
                    return;
                }
                newData.splice(index - 1, 0, newData.splice(index, 1)[0]);
            } else if (direction === 'bottom') {
                newData.push(newData.splice(index, 1)[0]);
            } else if (direction === 'down') {
                if (index === newData.length - 1) {
                    return;
                }
                newData.splice(index + 1, 0, newData.splice(index, 1)[0]);
            }
            orderLeatherSelectedData({
                id: this.orderId,
                orderLeatherMaterials: [...newData],
            }).then((res) => {
                if (res.data.code === 0) {
                    this.$toast.success('操作成功');
                    this.$emit('updateDataList', newData);
                } else {
                    this.$toast.error('操作失败');
                }
            }).catch(() => {
                this.$toast.error('操作失败');
            });
        },
        deleteItem(index) {
            uni.showLoading({
                title: '数据删除中...',
                mask: true,
            });
            deleteLeather(this.tableData[index].id)
                .then((res) => {
                    if (res.data.code === 0) {
                        this.$toast.success('删除成功');
                        this.$emit('delete', index);
                    } else {
                        this.$toast.error('删除失败');
                    }
                }).catch(() => {
                    this.$toast.error('删除失败');
                }).finally(() => {
                    uni.hideLoading();
                });
        },
    },
};
</script>

<style lang="scss">
.leather-list {
    height: calc(100% - 145rpx);
    .common-table {
        height: 100%;
        overflow-y: auto;
        box-sizing: border-box;
        .color {
            width: 120rpx;
        }
        .level {
            width: 120rpx;
        }
        .row-item {
            .flex {
                .info-name {
                    line-height: 30rpx;
                }
            }
            .level {
                padding: 10rpx 0;
                view {
                    line-height: 36rpx;
                }
            }
        }
        .line {
            width: 90rpx;
        }

    }
    .swipe-item .right-opt {
        background: #F56C6C;
        display: flex;
        text {
            padding: 0 20rpx;
            display: inline-block;
            width: 80rpx;
            height: 100%;
            color: #fff;
            font-size: 32rpx;
            font-weight: 500;
            line-height: 100rpx;
            text-align: center;
                background: #FF0000;
        }

        .opt-item {
            width: 80rpx;
            padding: 0 10rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #000;
            .opt-img {
                width: 48rpx;
                height: 48rpx;
            }
        }
    }
}
</style>
