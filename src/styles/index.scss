@import './uni-reset.scss';

* {
    box-sizing: border-box;
}

checkbox, radio {
    transform: scale(0.8);
}

.inline-block {
    display: inline-block;
}
.bold {
    font-weight: bold;
}
.page {
    background: black;
    width: 100vw;
    height: 100vh;
    box-sizing: border-box;
}

.common-table {
    height: 100%;
    padding-bottom: 20rpx;
    &__header {
        display: flex;
        align-items: center;
        height: 80rpx;
        line-height: 80rpx;
        color: #1A1A1A;
        border-bottom: 1px solid #1A1A1A;
        .th-item {
            text-align: center;
            border-left: 1px solid #1A1A1A;
            box-sizing: border-box;
            font-weight: bold;
            font-size: 30rpx;
        }
    }
    &__body {
        height: calc(100% - 80rpx);
        overflow: auto;
        .row-item {
            display: flex;
            align-items: center;
            height: 116rpx;
            background: #e8e8e8;
            font-size: 28rpx;
            box-sizing: border-box;
            &__column {
                height: 100%;
                line-height: 116rpx;
                text-align: center;
                border-left: 1px solid #fff;
                box-sizing: border-box;
            };
            .flex {
                display: inline-block;
                padding: 20rpx 16rpx;
                text-align: left;
                .info-name {
                    color: #1A1A1A;
                    line-height: 40rpx;
                    min-height: 40rpx;
                }
                .info-detail { 
                    display: flex;
                    justify-content: space-between;
                    line-height: 40rpx;
                    min-height: 40rpx;
                }
                .info-detail-item {
                    .title {
                        font-weight: bold;
                    }
                    .num {
                        color: #1A1A1A;
                    }
                    &:first-child {
                        margin-right: 20rpx;
                    }
                }
            }
            &.light-bg {
                background: #d9d9d9;
            }

        }
    }
    .selector {
        width: 80rpx;
    }
    .order {
        width: 80rpx;
    }
    .flex {
        flex: 1;
    }
    .area {
        width: 170rpx;
    }
    .opt {
        width: 80rpx;
    }
    .no-data {
        height: 120rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 32rpx;
        color: #1a1a1a;

    }
}
