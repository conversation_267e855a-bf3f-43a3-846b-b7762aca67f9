/* Element Chalk Variables */

// Special comment for theme configurator
// type|skipAutoTranslation|Category|Order
// skipAutoTranslation 1

/* Transition
-------------------------- */
// $--all-transition: all .3s cubic-bezier(.645,.045,.355,1) !default;
// $--fade-transition: opacity 300ms cubic-bezier(0.23, 1, 0.32, 1) !default;
// $--fade-linear-transition: opacity 200ms linear !default;
// $--md-fade-transition: transform 300ms cubic-bezier(0.23, 1, 0.32, 1), opacity 300ms cubic-bezier(0.23, 1, 0.32, 1) !default;
// $--border-transition-base: border-color .2s cubic-bezier(.645,.045,.355,1) !default;
// $--color-transition-base: color .2s cubic-bezier(.645,.045,.355,1) !default;
/* Color
-------------------------- */
/// color|1|Brand Color|0
$--color-primary: #0256FF !default;
/// color|1|Background Color|4
$--color-white: #FFFFFF !default;
/// color|1|Background Color|4
$--color-black: #000000 !default;
/// color|1|Brand Color|1-9
$--color-primary-light-1: #1687DC !default;
$--color-primary-light-2: mix($--color-white, $--color-primary-light-1, 20%) !default; // #459fe3
$--color-primary-light-3: mix($--color-white, $--color-primary-light-1, 30%) !default; // #5cabe7
$--color-primary-light-4: mix($--color-white, $--color-primary-light-1, 40%) !default; // #73b7ea
$--color-primary-light-5: mix($--color-white, $--color-primary-light-1, 50%) !default; // #8bc3ee
$--color-primary-light-6: mix($--color-white, $--color-primary-light-1, 60%) !default; // #a2cff1
$--color-primary-light-7: mix($--color-white, $--color-primary-light-1, 70%) !default; // #b9dbf5
$--color-primary-light-8: mix($--color-white, $--color-primary-light-1, 80%) !default; // #d0e7f8
$--color-primary-light-9: #F6F9FC !default;
$--color-primary-light-10: mix($--color-white, $--color-primary-light-1, 90%) !default; // #e8f3fc

/// color|1|Functional Color|1
$--color-success: #00AA64 !default;
/// color|1|Functional Color|1
$--color-warning: #F2A93E !default;
/// color|1|Functional Color|1
$--color-danger: #FF5500 !default;
/// color|1|Functional Color|1
$--color-info: #999999 !default;

$--color-success-light: #CEEFE1 !default;
$--color-warning-light: #FFE58F !default;
$--color-danger-light: #FFCEB6 !default;
$--color-info-light: #E9E9E9 !default;

$--color-success-lighter: #F1F9F6 !default;
$--color-warning-lighter: #FFEBE6 !default;
$--color-danger-lighter: #FFEEE6 !default;
$--color-info-lighter: #F3F3F3 !default;

/// 主要文字颜色 color|1|Font Color|2
$--color-text-primary: #333333 !default;
/// 常规文字颜色 color|1|Font Color|2
$--color-text-regular: #666666 !default;
/// 次要文字颜色 color|1|Font Color|2
$--color-text-secondary: #999999 !default;
/// 占位符文字颜色 color|1|Font Color|2
$--color-text-placeholder: #CCCCCC !default;
/// 基本边框 color|1|Border Color|3
$--border-color-base: #CCCCCC !default;
/// 浅边框 color|1|Border Color|3
$--border-color-light: #DDDDDD !default;
/// 浅边框 color|1|Border Color|3
$--border-color-lighter: #EEEEEE !default;
/// 超浅边框 color|1|Border Color|3
$--border-color-extra-light: #F8F8F8 !default;

// Background
/// 基本背景 color|1|Background Color|4
$--background-color-base: #F6F6F6 !default;
/// 常规背景
$--background-color-regular: #F8F8F8 !default;
/// 小块背景
$--background-color-secondary: #FBFBFB !default;

/* Link
-------------------------- */
$--link-color: $--color-primary !default;
$--link-hover-color: $--color-primary-light-1 !default;

/* Border
-------------------------- */
$--border-width-base: 1px !default;
$--border-style-base: solid !default;
$--border-color-hover: $--color-primary !default;
$--border-base: $--border-width-base $--border-style-base $--border-color-base !default;
/// borderRadius|1|Radius|0
$--border-radius-base: 2px !default;
/// borderRadius|1|Radius|0
// $--border-radius-small: 2px !default;
/// borderRadius|1|Radius|0
$--border-radius-circle: 100% !default;
/// borderRadius|1|Radius|0
// $--border-radius-zero: 0 !default;

// Box-shadow
/// boxShadow|1|Shadow|1
$--box-shadow-base: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04) !default;
// boxShadow|1|Shadow|1
// $--box-shadow-dark: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .12) !default;
/// boxShadow|1|Shadow|1
$--box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !default;

/* Fill
-------------------------- */
$--fill-base: $--color-white !default;

/* Typography
-------------------------- */
// $--font-path: 'fonts' !default;
// $--font-display: 'auto' !default;
/// fontSize|1|Font Size|0
// $--font-size-extra-large: 20px !default;
/// fontSize|1|Font Size|0
$--font-size-large: 18px !default;
/// fontSize|1|Font Size|0
$--font-size-medium: 16px !default;
/// fontSize|1|Font Size|0
$--font-size-base: 14px !default;
/// fontSize|1|Font Size|0
// $--font-size-small: 13px !default;
/// fontSize|1|Font Size|0
$--font-size-extra-small: 12px !default;
/// fontWeight|1|Font Weight|1
$--font-weight-primary: 400 !default;
/// fontWeight|1|Font Weight|1
// $--font-weight-secondary: 100 !default;
/// fontLineHeight|1|Line Height|2
$--font-line-height-primary: 24px !default;
/// fontLineHeight|1|Line Height|2
// $--font-line-height-secondary: 16px !default;
$--font-color-disabled-base: #BBBBBB !default;
/* Size
-------------------------- */
// $--size-base: 14px !default;

/* z-index
-------------------------- */
// $--index-normal: 1 !default;
// $--index-top: 1000 !default;
// $--index-popper: 2000 !default;
/* Disable base
-------------------------- */
$--disabled-fill-base: $--background-color-regular !default;
$--disabled-color-base: $--color-text-placeholder !default;
$--disabled-border-base: $--border-color-light !default;

/* Icon
-------------------------- */
// $--icon-color: #666 !default;
// $--icon-color-base: $--color-info !default;
