import Vue from 'vue';
import Vuex from 'vuex';
// import { getUserInfo } from 'api/user';
// let isWeWork = false;
// let loginText = '立即登录';
// #ifdef MP-WEIXIN
// isWeWork = wx.hasOwnProperty('qy');
// #endif
// #ifdef MP-ALIPAY
// loginText = '实名登录';
// #endif
Vue.use(Vuex);
const state = {
    cachedSelectedCpIds: [],
    cachedSelectedLeatherIds: [],
};
const mutations = {
    // changeLoading(state, data) {
    //     state.loading = data;
    // },
    setCacheSelectedCpIds(state, data) {
        state.cachedSelectedCpIds = data;
    },
    setCacheSelectedLeatherIds(state, data) {
        state.cachedSelectedLeatherIds = data;
    },
};

const getters = {
    // 小程序的sourceType，微信小程序和企业微信小程序下不同，但不区分是正式环境还是测试环境
    // mpSourceType(state) {
    //     let sourceType = 101;
    //     // #ifdef MP-WEIXIN
    //     const wxCode = 101;
    //     const weWorkCode = 201;
    //     sourceType = state.isWeWork ? weWorkCode : wxCode;
    //     // #endif
    //     // #ifdef MP-ALIPAY
    //     sourceType = 301;
    //     // #endif
    //     return sourceType;
    // },
};
const actions = {
    // setLoading({ commit }, bool) {
    //     commit('changeLoading', bool);
    //     if (bool) {
    //         uni.showLoading({
    //             title: '',
    //             mask: true,
    //         });
    //     } else {
    //         uni.hideLoading();
    //     }
    // },
};
export default new Vuex.Store({
    state,
    actions,
    getters,
    mutations,
});
