<template>
    <view class="page account-page">
        <!-- 用户信息列表 -->
        <view class="account-page__content" v-if="isLogin">
            <!-- 头像 -->
            <view class="info-row" @click="handleEditAvatar">
                <view class="row-label">头像</view>
                <view class="row-content avatar-content">
                    <image
                        class="avatar"
                        :src="userInfo.avatar || '/static/logo.svg'"
                        mode="aspectFill"
                    ></image>
                </view>
                <!-- <view class="row-arrow"><uni-icons type="forward" size="24" color="#999"></uni-icons></view> -->
            </view>

            <!-- 昵称 -->
            <view class="info-row" @click="openEdit('nickname')">
                <view class="row-label">昵称</view>
                <view class="row-content">{{ userInfo.username || '用户名最长12' }}</view>
                <view class="row-arrow"><uni-icons type="forward" size="24" color="#999"></uni-icons></view>
            </view>

            <!-- 角色 -->
            <view class="info-row">
                <view class="row-label">角色</view>
                <view class="row-content">{{ userInfo.role || '店铺管理员' }}</view>
            </view>

            <!-- 手机号 -->
            <view class="info-row" @click="openEdit('phone')">
                <view class="row-label">手机号</view>
                <view class="row-content">{{ formatPhone(userInfo.phone) || '138****8760' }}</view>
                <view class="row-arrow"><uni-icons type="forward" size="24" color="#999"></uni-icons></view>
            </view>

            <!-- 登录密码 -->
            <view class="info-row" @click="handleEditPassword">
                <view class="row-label">登录密码</view>
                <view class="row-content">修改密码</view>
                <view class="row-arrow"><uni-icons type="forward" size="24" color="#999"></uni-icons></view>
            </view>

            <!-- 邮箱 -->
            <view class="info-row" @click="openEdit('email')">
                <view class="row-label">邮箱</view>
                <view class="row-content">{{ userInfo.email || '暂无' }}</view>
                <view class="row-arrow"><uni-icons type="forward" size="24" color="#999"></uni-icons></view>
            </view>
        </view>

        <!-- 未登录状态 -->
        <view class="not-login" v-else>
            <uni-icons type="person" size="30"></uni-icons>
            <text class="tips">点击登录体验完整功能</text>
        </view>

        <!-- 编辑弹窗 -->
        <uni-popup ref="editPopup" type="center" background-color="#fff" @change="onPopupChange">
            <view class="edit-dialog">
                <view class="edit-title">{{ editTitle }}</view>
                <input v-if="editField !== 'email'"
                    class="edit-input"
                    :type="editType"
                    :placeholder="editPlaceholder"
                    v-model="editValue"
                    :maxlength="editMaxlength"
                />
                <input v-else
                    class="edit-input"
                    type="text"
                    :placeholder="editPlaceholder"
                    v-model="editValue"
                    :maxlength="editMaxlength"
                />
                <view class="edit-actions">
                    <button class="btn cancel" @click="closeEdit">取消</button>
                    <button class="btn confirm" :disabled="submitLoading" @click="submitEdit">{{ submitLoading ? '提交中...' : '确定' }}</button>
                </view>
            </view>
        </uni-popup>

        <!-- 密码修改弹窗 -->
        <uni-popup ref="passwordPopup" type="center" background-color="#fff">
            <view class="password-dialog">
                <view class="password-title">修改密码</view>
                <view class="password-form">
                    <view class="form-item">
                        <input
                            class="password-input"
                            type="password"
                            placeholder="请输入原密码"
                            v-model="passwordForm.originalPassword"
                            maxlength="20"
                        />
                    </view>
                    <view class="form-item">
                        <input
                            class="password-input"
                            type="password"
                            placeholder="请输入新密码"
                            v-model="passwordForm.password"
                            maxlength="20"
                        />
                    </view>
                    <view class="form-item">
                        <input
                            class="password-input"
                            type="password"
                            placeholder="请确认新密码"
                            v-model="passwordForm.confirmPassword"
                            maxlength="20"
                        />
                    </view>
                </view>
                <view class="password-actions">
                    <button class="btn cancel" @click="closePasswordPopup">取消</button>
                    <button class="btn confirm" :disabled="passwordLoading" @click="submitPasswordChange">
                        {{ passwordLoading ? '提交中...' : '确定' }}
                    </button>
                </view>
            </view>
        </uni-popup>

        <!-- 退出登录按钮 -->
        <view class="account-page__footer" v-if="isLogin">
            <button
                class="logout-btn"
                @click="logout"
            >退出登录</button>
        </view>

        <!-- 登录按钮 -->
        <view class="account-page__footer" v-else>
            <button
                class="login-btn"
                @click="goLogin"
            >立即登录</button>
        </view>

        <BottomTab :current-index="4"></BottomTab>
    </view>
</template>

<script>
import BottomTab from 'components/bottomTab/index.vue';
import { getUserInfo, updateUser, changePassword } from 'api/user';

export default {
    components: { BottomTab },
    data() {
        return {
            isLogin: true, // 登录状态
            userInfo: {      // 用户信息
                id: '',
                avatar: '',
                username: '',
                role: '',
                phone: '',
                email: '',
            },
            // 编辑弹窗相关
            editField: '',
            editTitle: '',
            editPlaceholder: '',
            editType: 'text',
            editValue: '',
            editMaxlength: 50,
            submitLoading: false,
            // 密码修改相关
            showPasswordPopup: false,
            passwordForm: {
                originalPassword: '',
                password: '',
                confirmPassword: '',
            },
            passwordLoading: false,
        };
    },
    onShow() {
        // 检查登录状态（可从本地存储获取）
        this.getUserInfo();
    },
    methods: {
        // 检查登录状态
        getUserInfo() {
            const token = uni.getStorageSync('accessToken');
            if (token) {
                // 实际开发中此处应调用接口获取用户信息
                getUserInfo().then((res) => {
                    const { code, data } = res.data;
                    if (code === 0) {
                        const { roles, id, file } = data;
                        this.userInfo = {
                            id: id,
                            file,
                            avatar: data.avatar !== 'null' ? data.avatar : '/static/logo.svg',
                            username: data.nickname,
                            role: roles.find(r => r.id === id)?.roleName || '店铺管理员',
                            phone: data.phone || '',
                            email: data.email || '',
                        };
                    }
                });
                this.isLogin = true;
            } else {
                this.isLogin = false;
                this.userInfo = {};
            }
        },

        // 打开编辑弹窗
        openEdit(field) {
            this.editField = field;
            if (field === 'nickname') {
                this.editTitle = '修改昵称';
                this.editPlaceholder = '请输入1-12位昵称';
                this.editType = 'text';
                this.editValue = this.userInfo.username || '';
                this.editMaxlength = 12;
            } else if (field === 'phone') {
                this.editTitle = '修改手机号';
                this.editPlaceholder = '请输入11位手机号';
                this.editType = 'number';
                this.editValue = this.userInfo.phone || '';
                this.editMaxlength = 11;
            } else if (field === 'email') {
                this.editTitle = '修改邮箱';
                this.editPlaceholder = '请输入邮箱地址';
                this.editType = 'text';
                this.editValue = this.userInfo.email || '';
                this.editMaxlength = 50;
            }
            this.$refs.editPopup.open();
        },
        closeEdit() {
            this.$refs.editPopup.close();
        },
        onPopupChange() {},

        // 表单校验
        validateEdit() {
            const val = (this.editValue || '').trim();
            if (this.editField === 'nickname') {
                if (!val || val.length < 1 || val.length > 12) {
                    uni.showToast({ title: '昵称需为1-12位', icon: 'none' });
                    return false;
                }
            } else if (this.editField === 'phone') {
                if (!/^1\d{10}$/.test(val)) {
                    uni.showToast({ title: '请输入正确手机号', icon: 'none' });
                    return false;
                }
            } else if (this.editField === 'email') {
                const emailReg = /^[\w.-]+@[\w.-]+\.[A-Za-z]{2,}$/;
                if (!emailReg.test(val)) {
                    uni.showToast({ title: '请输入正确邮箱', icon: 'none' });
                    return false;
                }
            }
            return true;
        },

        // 提交编辑
        async submitEdit() {
            if (!this.validateEdit()) {
                return;
            }
            this.submitLoading = true;
            try {
                const { email, nickname, phone, id, file } = this.userInfo;
                const payload = {
                    email, nickname, phone, id, file,
                    [this.editField]: this.editValue.trim(),
                };
                await updateUser(payload);
                uni.showToast({ title: '修改成功', icon: 'success' });
                this.closeEdit();
                // 重新拉取用户信息
                setTimeout(() => this.getUserInfo(), 300);
            } catch (e) {
                console.log('sss', e);
                uni.showToast({ title: '修改失败', icon: 'error' });
                // 失败提示由拦截器统一处理
            } finally {
                this.submitLoading = false;
            }
        },

        // 格式化手机号
        formatPhone(phone) {
            if (!phone) {
                return '';
            }
            return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
        },

        // 编辑头像
        handleEditAvatar() {
            uni.showToast({
                title: '头像编辑功能',
                icon: 'none',
            });
        },

        // 编辑密码
        handleEditPassword() {
            this.showPasswordPopup = true;
            this.$refs.passwordPopup.open();
        },

        // 跳转登录
        goLogin() {
            uni.navigateTo({
                url: '/pages/login/index',
            });
        },
        // 退出登录
        logout() {
            uni.showModal({
                title: '提示',
                content: '确定要退出登录吗？',
                success: res => {
                    if (res.confirm) {
                        uni.removeStorageSync('accessToken');
                        this.isLogin = false;
                        this.userInfo = {};
                        uni.showToast({
                            title: '已退出',
                            icon: 'success',
                        });
                    }
                },
            });
        },

        // 关闭密码修改弹窗
        closePasswordPopup() {
            this.showPasswordPopup = false;
            this.$refs.passwordPopup.close();
            // 清空表单
            this.passwordForm = {
                originalPassword: '',
                password: '',
                confirmPassword: '',
            };
        },

        // 提交密码修改
        async submitPasswordChange() {
            const { originalPassword, password, confirmPassword } = this.passwordForm;

            // 表单验证
            if (!originalPassword.trim()) {
                uni.showToast({ title: '请输入原密码', icon: 'none' });
                return;
            }
            if (!password.trim()) {
                uni.showToast({ title: '请输入新密码', icon: 'none' });
                return;
            }
            if (password.length < 6) {
                uni.showToast({ title: '新密码至少6位', icon: 'none' });
                return;
            }
            if (password !== confirmPassword) {
                uni.showToast({ title: '两次密码输入不一致', icon: 'none' });
                return;
            }

            try {
                this.passwordLoading = true;
                const res = await changePassword({
                    originalPassword: originalPassword.trim(),
                    password: password.trim(),
                    confirmPassword: confirmPassword.trim(),
                });

                const { code, message } = res.data;
                if (code === 0) {
                    uni.showToast({ title: '密码修改成功', icon: 'success' });
                    this.closePasswordPopup();
                    // 密码修改成功后，建议用户重新登录
                    setTimeout(() => {
                        uni.showModal({
                            title: '提示',
                            content: '密码修改成功，建议重新登录以确保安全',
                            showCancel: false,
                            success: () => {
                                this.logout();
                            },
                        });
                    }, 1000);
                } else {
                    uni.showToast({ title: message || '密码修改失败', icon: 'none' });
                }
            } catch (error) {
                uni.showToast({ title: '密码修改失败，请重试', icon: 'none' });
            } finally {
                this.passwordLoading = false;
            }
        },
    },
};
</script>

<style lang="scss">
.account-page {
    min-height: 100vh;
    background-color: #f5f5f5;
    padding-bottom: 120rpx;
    padding-top: 20rpx;

    &__content {
        background-color: #fff;
    }

    .info-row {
        display: flex;
        align-items: center;
        padding: 30rpx 40rpx;
        border-bottom: 1rpx solid #f0f0f0;
        background-color: #fff;

        &:last-child {
            border-bottom: none;
        }

        .row-label {
            color: #000;
            font-size: 32rpx;
            width: 160rpx;
            flex-shrink: 0;
            font-weight: 700;
        }

        .row-content {
            flex: 1;
            color: #767676;
            font-size: 32rpx;
            text-align: right;
            margin-right: 20rpx;
            // height: 100rpx;
            &.avatar-content {
                height: 80rpx;
            }

            .avatar {
                width: 80rpx;
                height: 80rpx;
                border-radius: 50%;
                background-color: #f0f0f0;
            }
        }

        .row-arrow {
            color: #ccc;
            font-size: 32rpx;
            font-weight: bold;
        }

        // 可点击行的样式
        &:active {
            background-color: #f8f8f8;
        }
    }

    .edit-dialog {
        width: 620rpx;
        padding: 40rpx 30rpx;
        box-sizing: border-box;
        background: #fff;
        border-radius: 16rpx;
        .edit-title {
            font-size: 34rpx;
            color: #333;
            text-align: center;
            margin-bottom: 30rpx;
        }
        .edit-input {
            border: 1px solid #eee;
            border-radius: 8rpx;
            padding: 20rpx;
            font-size: 30rpx;
            color: #333;
            background: #fff;
        }
        .edit-actions {
            margin-top: 30rpx;
            display: flex;
            justify-content: space-between;
            .btn {
                flex: 1;
                height: 80rpx;
                line-height: 80rpx;
                font-size: 30rpx;
                border-radius: 8rpx;
            }
            .cancel {
                margin-right: 20rpx;
                background: #f5f5f5;
                color: #666;
            }
            .confirm {
                background: #127FD2;
                color: #fff;
            }
        }
    }

    .password-dialog {
        width: 620rpx;
        padding: 40rpx 30rpx;
        box-sizing: border-box;
        background: #fff;
        border-radius: 16rpx;

        .password-title {
            font-size: 36rpx;
            font-weight: 600;
            color: #333;
            text-align: center;
            margin-bottom: 40rpx;
            padding-bottom: 20rpx;
            border-bottom: 1rpx solid #f0f0f0;
        }

        .password-form {
            .form-item {
                margin-bottom: 24rpx;

                &:last-child {
                    margin-bottom: 0;
                }
            }

            .password-input {
                width: 100%;
                height: 80rpx;
                border: 2rpx solid #e0e0e0;
                border-radius: 12rpx;
                padding: 0 24rpx;
                font-size: 30rpx;
                color: #333;
                background: #fff;
                box-sizing: border-box;
                transition: border-color 0.3s ease;

                &:focus {
                    border-color: #127FD2;
                    outline: none;
                }

                &::placeholder {
                    color: #999;
                    font-size: 28rpx;
                }
            }
        }

        .password-actions {
            margin-top: 40rpx;
            display: flex;
            justify-content: space-between;
            gap: 20rpx;

            .btn {
                flex: 1;
                height: 88rpx;
                line-height: 88rpx;
                font-size: 32rpx;
                border-radius: 12rpx;
                border: none;
                font-weight: 500;
                transition: all 0.3s ease;

                &:disabled {
                    opacity: 0.6;
                    cursor: not-allowed;
                }

                &:active:not(:disabled) {
                    transform: scale(0.98);
                }
            }

            .cancel {
                background: #f5f5f5;
                color: #666;

                &:active {
                    background: #e8e8e8;
                }
            }

            .confirm {
                background: #127FD2;
                color: #fff;

                &:active {
                    background: #0f6bb8;
                }
            }
        }
    }

    .not-login {
        display: flex;
        align-items: center;
        justify-content: center;
        color: #999;
        height: 400rpx;
        background-color: #fff;
        margin-top: 20rpx;

        .tips {
            margin-left: 20rpx;
            font-size: 32rpx;
        }
    }

    &__footer {
        position: fixed;
        bottom: 120rpx;
        left: 0;
        width: 100%;
        padding: 0 40rpx;
        box-sizing: border-box;

        .logout-btn, .login-btn {
            width: 100%;
            height: 88rpx;
            background-color: #fff;
            color: #999;
            border: none;
            border-radius: 8rpx;
            font-size: 32rpx;
            display: flex;
            align-items: center;
            justify-content: center;

            &:active {
                background-color: #f8f8f8;
            }
            &::after {
                border: none;
            }
        }
    }
}
</style>
