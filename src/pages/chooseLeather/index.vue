<template>
    <view class="page choose-leather">
        <view class="choose-leather__content">
            <view class="common-table">
                <view class="common-table__header">
                    <view class="th-item selector">选择</view>
                    <view class="th-item flex">皮料名</view>
                    <view class="th-item color">色调</view>
                    <view class="th-item level">分级</view>
                    <view class="th-item line">布纹线</view>
                    <view class="th-item opt">操作</view>
                </view>
                <view class="common-table__body">
                    <view class="row-item"
                        v-for="(item, index) in tableData"
                        :key="index"
                        @click="handleClickRow(item)"
                        :class="{'light-bg': index%2, 'active': activeRowId === item.id, 'disabled': item.usedFlag}"
                    >
                        <view class="row-item__column selector">
                            <checkbox class="selector-checkbox"
                                v-if="!item.usedFlag"
                                :value="item.code"
                                :checked="item.isAdd"
                                color="#127FD2"
                                @click="handleSelect(item)"
                            />
                            <text v-else>{{ item.status === 2 ? '已排版' : item.status === 3 ? '已切割' : '使用中' }}</text>
                        </view>
                        <view class="row-item__column flex">
                            <view class="info-name">{{ item.leatherMaterialName }}</view>
                            <view class="info-detail">{{ item.totalArea }} ft²</view>
                        </view>
                        <view class="row-item__column color">{{ item.color || '-' }}</view>
                        <view class="row-item__column level">
                            <view>{{ item.plLevel || '-' }}</view>
                            <view>{{ item.plLevelRate || '-' }}%</view>
                        </view>
                        <view class="row-item__column line">{{ item.textureLineAngle || '-' }}</view>
                        <view class="row-item__column opt" @click="handleToViewSvg(item)">查看</view>
                    </view>
                    <view class="no-data" v-if="!loading && !tableData.length">
                        暂无数据
                    </view>
                </view>
            </view>
        </view>
        <view class="choose-leather__bottom">
            <view class="opt-row">
                <view class="opt-left">
                    <checkbox class="selector-checkbox"
                        v-if="!tableData.every(a => a.usedFlag)"
                        :value="true"
                        :checked="selectAll"
                        color="#127FD2"
                        @click="handleSelectAll"
                    ></checkbox>
                    <text class="all-text" v-if="!tableData.every(a => a.usedFlag)">全选</text>
                </view>
                <view class="select-info">
                    <view>已选择：{{ tableData.filter(a => a.isAdd).length }}</view>
                    <view>总面积：{{ totalArea }}</view>
                </view>
                <button type="primary" :disabled="saveBtnDisabled" class="submit-btn" @click="saveSelect">
                    确认选择
                </button>
            </view>
        </view>
    </view>
</template>

<script>
import { getBatchLeatherSelectedList, saveLeatherSelectedData } from 'api/order';
import { mapMutations, mapState } from 'vuex';

export default {
    data() {
        return {
            loading: false,
            tableData: [],
            orderId: '',
            batchId: '',
            supplierName: '',
            checkedCpIds: [],
            selectAll: false,
            activeRowId: '',
            saveBtnDisabled: false,
        };
    },
    computed: {
        ...mapState(['cachedSelectedLeatherIds']),
        totalArea() {
            return this.tableData.filter(a => a.isAdd).reduce((total, item) => {
                return total + item.totalArea;
            }, 0).toFixed(3);
        },
    },
    onLoad(query) {
        this.orderId = query.orderId;
        this.batchId = query.batchId;
        this.supplierName = decodeURIComponent(query.supplierName);
        if (!this.orderId || !this.batchId) {
            return;
        }
        uni.setNavigationBarTitle({
            title: decodeURIComponent(this.supplierName),
        });
    },
    async onShow() {
        this.saveBtnDisabled = false;
        this.selectAll = false;
        this.activeRowId = '';
        await this.getData();
    },
    methods: {
        ...mapMutations(['setCacheSelectedLeatherIds']),
        handleToViewSvg(item) {
            if (this.loading) {
                return;
            }
            this.setCacheSelectedLeatherIds(this.tableData.filter(a => a.isAdd).map(a => a.id));
            uni.navigateTo({
                url:
                    `pages/viewSvg/index?pageName=${(item.leatherMaterialName)}&plId=${item.id}`,
            });
        },
        handleClickRow(item) {
            this.activeRowId = item.id;
        },
        handleSelectAll() {
            this.saveBtnDisabled = false;
            this.selectAll = !this.selectAll;
            this.tableData.forEach(a => {
                if (!a.usedFlag) {
                    a.isAdd = this.selectAll;
                }
            });
        },
        handleSelect(item) {
            this.saveBtnDisabled = false;
            item.isAdd = !item.isAdd;
            if (this.tableData.every(a => a.isAdd || a.usedFlag)) {
                this.selectAll = true;
            } else {
                this.selectAll = false;
            }
        },
        getData() {
            this.loading = true;
            uni.showLoading({
                title: '加载中...',
                mask: true,
            });
            getBatchLeatherSelectedList({
                batchId: this.batchId,
                orderId: this.orderId,
            }).then(res => {
                const { code, data = [] } = res.data;
                if (code === 0) {
                    this.tableData = data;

                    if (this.cachedSelectedLeatherIds.length > 0) {
                        this.tableData.forEach(a => {
                            a.isAdd = this.cachedSelectedLeatherIds.includes(a.id);
                        });
                        this.$nextTick(() => {
                            this.setCacheSelectedLeatherIds([]);
                        });
                    }
                    this.selectAll = this.tableData.every(a => a.usedFlag || a.isAdd);
                } else {
                    this.$toast.error('获取数据失败');
                }
            }).catch(() => {
                this.$toast.error('获取数据失败');
            }).finally(() => {
                uni.hideLoading();
                this.loading = false;
                this.$nextTick(() => {
                    this.setCacheSelectedLeatherIds([]);
                });
            });
        },
        saveSelect() {
            if (this.saveBtnDisabled) {
                return;
            }
            this.saveBtnDisabled = true;
            uni.showLoading({
                title: '加载中...',
                mask: true,
            });
            const params = {
                id: this.orderId,
                orderLeatherMaterials: this.tableData.filter(a => a.isAdd).map(a => ({
                    batchId: a.batchId,
                    leatherMaterialId: a.id,
                    color: a.color,
                    textureLineAngle: a.textureLineAngle,
                    area: a.area,
                    productGrade: a.productGrade,
                })),
            };
            saveLeatherSelectedData(params).then(res => {
                const { code } = res.data;
                if (code === 0) {
                    this.$toast.success('设置成功');
                    uni.navigateBack({
                        url: '/pages/addLeather/index?orderId=' + this.orderId,
                    });
                } else {
                    this.saveBtnDisabled = false;
                    this.$toast.error('设置失败');
                }
            }).catch(() => {
                this.saveBtnDisabled = false;
                this.$toast.error('设置失败');
            }).finally(() => {
                uni.hideLoading();
            });
        },
    },
};
</script>

<style lang="scss">
.choose-leather {
    height: 100vh;
    background: #fff;
    &__content {
        height: calc(100% - 140rpx);
        .common-table {
            .area-info {
                font-weight: bold;
            }
            .row-item__column {
                font-size: 24rpx;

                &.opt {
                    color: #127FD2;
                    text-decoration: underline;
                }
            }
            .flex .info-name {
                line-height: 40rpx;
            }
            .color {
                width: 90rpx;
            }
            .level {
                width: 120rpx;
            }

            .line {
                width: 90rpx;
            }
            .selector {
                width: 85rpx;
            }
            .row-item {
                .level {
                    padding: 10rpx 0;
                    display: flex;
                    flex-direction: column;
                    line-height: 42rpx;
                }
            }
            .row-item.disabled {
                color: #9E9B9B;
                .info-name {
                    color: #9E9B9B;
                }
            }
            .row-item.active {
                border: 5px solid #02A7F0;
                .row-item__column:not(.flex) {
                    line-height: 80rpx;
                }
                .info-name {
                    padding-top: 2px;
                }
                .selector {
                    width: 75rpx;
                    border: none;
                    text {
                        margin-left: -3px;
                    }
                }
                .level {
                    line-height: 42rpx !important;
                    padding: 0;
                }
                .opt{
                    width: 70rpx;
                    padding-left: 5px;
                }
            }
        }

    }
    &__bottom {
        width: 100vw;
        height: 140rpx;
        position: absolute;
        bottom: 0;
        background: #000;
        padding-bottom: 10rpx;
        .opt-row {
            padding: 0 20rpx;
            width: 100%;
            height: 140rpx;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: #fff;
            font-size: 28rpx;
            box-sizing: border-box;
            .opt-left {
                display: flex;
                align-items: center;
            }
            .view-svg {
                font-size: 28rpx;
                color: #02A7F0;
                text-decoration: underline;
            }
            button {
                width: 190rpx;
                height: 70rpx;
                font-size: 32rpx;
                line-height: 70rpx;
                margin: 0;
                background:#0256FF;
            }
        }
        .mini-preview {
            position: absolute;
            right: 0;
            background: #000;
            bottom: 144rpx;
            padding: 8rpx 12rpx;
            width: 340rpx;
            height: 290rpx;
            .preview-img {
                width: 100%;
                height: 100%;
            }
            .info-icon {
                position: absolute;
                right: 10rpx;
                top: -60rpx;
            }
        }
    }
}
</style>
