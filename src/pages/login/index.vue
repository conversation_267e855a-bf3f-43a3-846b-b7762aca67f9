<template>
    <view class="page login-page">
        <view class="login-page__header">
            <image class="logo" src="/static/logo.svg"></image>
            <p class="">· 数智云脑 智享未来 ·</p>
        </view>
        <view class="login-page__content">
            <form class="login-form">
                <view class="login-form__items">
                    <view class="title">用户名</view>
                    <input class="input"
                        name="input"
                        placeholder="请输入邮箱或手机号"
                        v-model="formData.userName"
                    />
                </view>
                <view class="login-form__items">
                    <view class="title">登录密码</view>
                    <input class="input"
                        name="input"
                        placeholder="请输入登录密码"
                        v-model="formData.password"
                    />
                </view>
                <button :loading="loginSubmitLoading"
                    class="submit-btn"
                    :disabled="quickLoginLoading"
                    type="primary"
                    @click="toLogin(true)"
                >登 录</button>

                <!--                <div class="btn-box">-->
                <!--                    <button class="quickLoginBtn submit-btn"-->
                <!--                        :disabled="loginSubmitLoading"-->
                <!--                        :loading="quickLoginLoading"-->
                <!--                        open-type="getPhoneNumber"-->
                <!--                        type="primary"-->
                <!--                        @getphonenumber="getWxPhoneNumber"-->
                <!--                        @error="handleError"-->
                <!--                    >一键登录</button>-->
                <!--                </div>-->
            </form>
        </view>
    </view>
</template>

<script>
import { fastLogin, login } from 'api/user.js';

export default {
    data() {
        return {
            title: 'Hello',
            formData: {
                userName: '',
                password: '',
                // userName: '18551809849',
                // password: '123456',
            },
            loginSubmitLoading: false,
            quickLoginLoading: false,
        };
    },
    methods: {
        handleError(err) {
            console.log('err ===== ', err);
        },
        getWxPhoneNumber(e) {
            console.log('getWxPhoneNumber ===== ', e);
            this.quickLoginLoading = true;
            const that = this;
            if (e.detail.errMsg === 'getPhoneNumber:ok') {
                const { encryptedData, iv } = e.detail;
                wx.login({
                    success({ code }) {
                        fastLogin({ code, encryptedData, iv }).then(({ data }) => {
                            that.handleLoginSuccess(data.data);
                        }).catch(err => {
                            console.log('err', err);
                            that.quickLoginLoading = false;
                        });
                    },
                    fail(e) {
                        console.log('fail', e);
                    },

                });
                return;
            }
            this.quickLoginLoading = false;
        },
        toLogin() {
            if (!this.formData.userName || !this.formData.password) {
                uni.showToast({
                    title: '请输入用户名和密码',
                    icon: 'none',
                });
                return;
            }
            login(this.formData).then((res) => {
                if (res.data?.code === 0) {
                    this.handleLoginSuccess(res.data?.data);
                } else {
                    uni.showToast({
                        title: '登录失败，' + res.data?.message,
                        icon: 'none',
                    });
                }
            }).catch(err => {
                console.log('err', err);
            });
        },
        handleLoginSuccess(data) {
            uni.showToast({
                title: '登录成功',
                icon: 'none',
            });
            uni.setStorageSync('accessToken', data);
            const loginSuccessPage = uni.getStorageSync('loginSuccessPage');
            uni.reLaunch({
                url: loginSuccessPage ? decodeURIComponent(loginSuccessPage) : '/pages/home/<USER>',
                success: () => {
                    loginSuccessPage && uni.removeStorageSync('loginSuccessPage');
                },
            });
        },
    },
    onLoad() {
    },
};
</script>

<style lang="scss">
	.login-page {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
        color: #fff;
        &__header {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin-bottom: 40rpx;
            .logo {
                width: 300rpx;
                height: 300rpx;
            }
        }
        &__content {
            padding: 10rpx 30rpx;
            width: 100vw;
        }
        .login-form {
            width: 100vw;
            &__items {
                display: flex;
                flex-direction: column;
                margin-bottom: 20rpx;
                padding: 0 50rpx;
                .title {
                    font-size: 30rpx;
                    color: #fff;
                    margin-bottom: 15rpx;
                }
                .input {
                    height: 80rpx;
                    padding: 0 40rpx;
                    border-radius: 10rpx;
                    background: #fff;
                    color: black;
                    font-size: 30rpx;
                    margin-bottom: 20rpx;
                }
            }
            .submit-btn {
                width: auto;
                background: #FFFFFF;
                color: black;
                height: 80rpx;
                line-height: 80rpx;
                margin: 60rpx 50rpx 30rpx;
            }

            .btn-box{
                position: relative;
                margin-top: 100rpx;
                .btn-modal{
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    top: 0;
                    z-index: 9;
                }
                .quickLoginBtn{
                    color: #127FD2;
                }
            }
        }
	}
</style>
