<template>
    <view class="page style-list">
        <view class="style-list__filter">
            <input
                class="style-name-input"
                v-model="styleName"
                placeholder="请输入款式名称"
            />
            <uni-icons class="fab-icon" type="search" color="#fff" size="30" @click="getStyleData"></uni-icons>
        </view>
        <view class="style-list__content">
            <view class="common-table">
                <view class="common-table__header">
                    <view class="th-item flex">款式名</view>
                    <view class="th-item area">面积(ft²)</view>
                    <view class="th-item create-time">创建时间</view>
                    <!-- <view class="th-item valid-date">有效期</view> -->
                    <view class="th-item option">已添加</view>
                </view>
                <view class="common-table__body">
                    <view class="row-item" v-for="(item, index) in tableData" :key="index" :class="{'light-bg': index%2}">
                        <view class="row-item__column flex">
                            <view class="info-name">{{ item.styleName }}</view>
                            <view class="info-detail">
                                <text class="info-detail-item">
                                    <text class="title">项目名: &nbsp;</text>
                                    <text class="num project-name">{{ item.projectName }}</text>
                                </text>
                                <text class="info-detail-item">
                                    <text class="title">裁片数: &nbsp;</text>
                                    <text class="num">{{ item.quantity || 0 }}</text>
                                </text>
                            </view>
                        </view>
                        <view class="row-item__column area">{{ item.totalArea || 0 }}</view>
                        <view class="row-item__column create-time">{{ formateValidation(item.createTime) }}</view>
                        <view class="row-item__column option" @click="handleToChoose(item)">
                            {{ item.userCount || 0 }}
                        </view>
                    </view>
                    <view class="no-data" v-if="!loading && !tableData.length">
                        暂无数据
                    </view>
                </view>
            </view>
        </view>
        <view class="style-list__footer">
            <button type="primary" @click="handleBack">
                <uni-icons class="back-icon" type="arrow-left" color="#fff" size="24"></uni-icons> 返回
            </button>
        </view>
    </view>
</template>

<script>
import { getStyleList } from 'api/order';
import dayjs from 'dayjs';

export default {
    data() {
        return {
            loading: false,
            tableData: [],
            styleName: '',
            current: 1,
            size: 1000,
            total: 0,
            orderId: '',
        };
    },
    async onLoad(query) {
        this.orderId = query.orderId;
        if (!this.orderId) {
            return;
        }
    },
    async onShow() {
        await this.getStyleData();
    },
    methods: {
        handleBack() {
            uni.reLaunch({
                url:
                    `pages/config/index?orderId=${this.orderId}&activeTab=1`,
            });
        },
        handleToChoose(item) {
            uni.navigateTo({
                url:
                    `/pages/chooseStyle/index?id=${item.id}&orderId=${this.orderId}&styleName=${(item.styleName)}`,
            });
        },
        formateValidation(value) {
            if (!value) {
                return '';
            }
            return dayjs(new Date(value)).format('YYYY/MM/DD');
        },
        getStyleData() {
            this.loading = true;

            uni.showLoading({
                title: '加载中...',
                mask: true,
            });
            getStyleList({
                styleName: this.styleName,
                orderId: this.orderId,
                size: this.size,
                current: this.current,
            }).then(res => {
                const { code, data = {} } = res.data;
                if (code === 0) {
                    this.tableData = data.records;
                    this.total = data.total;
                } else {
                    this.$toast({
                        title: '获取信息失败',
                        icon: 'none',
                    });
                }
            }).catch((err) => {
                this.$toast({
                    title: err.message || '获取信息失败',
                    icon: 'none',
                });
            }).finally(() => {
                uni.hideLoading();
                this.loading = false;
            });
        },
    },
};
</script>

<style lang="scss">
.style-list {
    height: 100vh;
    background: #fff;
    &__filter {
        height: 120rpx;
        background: #000;
        padding: 10rpx 40rpx;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .style-name-input {
            color: #fff;
            font-size: 16px;
            width: 90%;
        }
    }
    &__content {
        height: calc(100vh - 230rpx);
        .common-table {
            .row-item__column {
                font-size: 24rpx;
                &.option {
                    color: #127FD2;
                }
                &.flex {
                    min-width: 0; // allow children to shrink within flex
                    .info-name {
                        width: 100%;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                }
                .project-name {
                    max-width: 126rpx;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    white-space: nowrap;
                    display: inline-block;
                    vertical-align: bottom;
                }
            }
            .area {
                width: 120rpx;
                display: flex;
                align-items: center;
                word-break: break-all;
                justify-content: center;
            }
            .row-item .area{
                line-height: 1;
            }
            .option {
                width: 100rpx;
            }
            .valid-date {
                width: 170rpx;
                line-height: 1;
                padding: 28rpx 0;
            }
            .create-time {
                width: 150rpx;
            }
        }

    }
    &__footer {
        height: 100rpx;
        padding-left: 20rpx;
        padding-bottom: 20rpx;
        display: flex;
        justify-content: left;
        button {
            width: 190rpx;
            margin: 0;
            padding-left: 80rpx;
            height: 70rpx;
            line-height: 70rpx;
            font-size: 32rpx;
            text-align: center;
            background: #0256FF;
            .back-icon {
                position: absolute;
                left: 30rpx;
            }
        }
    }
}
</style>
