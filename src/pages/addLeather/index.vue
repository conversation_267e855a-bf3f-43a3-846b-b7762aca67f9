<template>
    <view class="page leather-list">
        <view class="leather-list__filter">
            <input
                class="query-input"
                v-model="keyword"
                placeholder="搜索皮料批次ID/供应商/采购编号"
            />
            <uni-icons class="fab-icon" type="search" color="#fff" size="30" @click="getData"></uni-icons>
        </view>
        <view class="leather-list__content">
            <view class="common-table">
                <view class="common-table__header">
                    <view class="th-item flex">批次ID/总面积/供应商/采购编号</view>
                    <view class="th-item count">色调数</view>
                    <view class="th-item expect-time">预计日期</view>
                    <view class="th-item option">已添加</view>
                </view>
                <view class="common-table__body">
                    <view class="row-item" v-for="(item, index) in tableData" :key="index" :class="{'light-bg': index%2}">
                        <view class="row-item__column flex">
                            <view class="info-detail">
                                <text class="info-detail-item">
                                    <text class="title">{{ item.batchName }}</text>
                                </text>
                                <text class="info-detail-item">
                                    <text class="title">总面积: &nbsp;</text>
                                    <text class="num">{{ item.plTotalArea.toFixed(3) }}</text>
                                </text>
                            </view>
                            <view class="info-detail">
                                <text class="info-detail-item">
                                    <text class="num">{{ item.supplierName }}</text>
                                </text>
                                <text class="info-detail-item">
                                    <text class="title">采购号: &nbsp;</text>
                                    <text class="num">{{ item.purchaseNo || '-' }}</text>
                                </text>
                            </view>
                        </view>
                        <view class="row-item__column count">{{ item.totalColor }}</view>
                        <view class="row-item__column expect-time">
                            {{ formateValidation(item.expectTime) }}
                        </view>
                        <view class="row-item__column option" @click="handleToChoose(item)">
                            {{ item.addedQuantity || 0 }}
                        </view>
                    </view>
                    <view class="no-data" v-if="!tableData.length">
                        暂无数据
                    </view>
                </view>
            </view>
            <uni-pagination v-show="!loading && tableData.length > 10"
                class="common-table-pagination"
                :total="total"
                :current="current"
                :pageSize="size"
            />
        </view>

        <view class="leather-list__footer">
            <button type="primary" @click="handleBack">
                <uni-icons class="back-icon" type="arrow-left" color="#fff" size="24"></uni-icons> 返回
            </button>
        </view>
    </view>
</template>

<script>
import { getBatchLetherList } from 'api/order';
import dayjs from 'dayjs';

export default {
    data() {
        return {
            loading: false,
            tableData: [
                {
                    id: 1,
                    styleName: 'KUANSHIMING',
                    projectName: '123',
                    cpQuantity: '12345',
                    totalArea: '123.23',
                    userCount: '123',
                    styleValidityPeriodStart: '2019-08-24T14:15:22.123Z',
                    styleValidityPeriodEnd: '2019-08-24T14:15:22.123Z',
                },
            ],
            keyword: '',
            orderId: '',
            current: 1,
            size: 10,
            total: 0,
        };
    },
    async onLoad(query) {
        this.orderId = query.orderId;
        if (!this.orderId) {
            return;
        }
    },
    async onShow() {
        await this.getData();
    },
    methods: {
        handleBack() {
            uni.reLaunch({
                url:
                    `pages/config/index?orderId=${this.orderId}&activeTab=2`,
            });
        },
        handleToChoose(item) {
            uni.navigateTo({
                url:
                    `/pages/chooseLeather/index?batchId=${item.id}&orderId=${this.orderId}&supplierName=${(item.supplierName)}`,
            });
        },
        formateValidation(value) {
            if (!value) {
                return '';
            }
            return dayjs(new Date(value)).format('YYYY/MM/DD');
        },
        getData() {
            this.loading = true;
            uni.showLoading({
                title: '加载中...',
                mask: true,
            });
            getBatchLetherList({
                keyword: this.keyword,
                orderId: this.orderId,
                size: this.size,
                current: this.current,
            }).then(res => {
                const { code, data = {} } = res.data;
                if (code === 0) {
                    this.tableData = data.records;
                    this.total = data.total;
                } else {
                    this.$toast({
                        title: '获取信息失败',
                        icon: 'none',
                    });
                }
            }).catch((err) => {
                this.$toast({
                    title: err.message || '获取信息失败',
                    icon: 'none',
                });
            }).finally(() => {
                uni.hideLoading();
                this.loading = false;
            });
        },
    },
};
</script>

<style lang="scss">
.leather-list {
    height: 100vh;
    background: #fff;
    &__filter {
        height: 120rpx;
        background: #000;
        padding: 10rpx 40rpx;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .query-input {
            color: #fff;
            font-size: 16px;
            width: 90%;
        }
    }
    &__content {
        height: calc(100vh - 230rpx);
        .common-table {
            &__header {
                .th-item {
                    font-size: 26rpx;
                }
            }
            .row-item__column {
                font-size: 24rpx;
                &.option {
                    color: #127FD2;
                }
                .info-detail {
                    display: flex;
                    justify-content: space-between;
                    .title {
                        padding-right: 10rpx;
                    }
                }
            }
            .count {
                width: 100rpx;
            }
            .option {
                width: 100rpx;
            }
            .expect-time {
                width: 150rpx;
            }
        }

    }
    &__footer {
        height: 100rpx;
        padding-left: 20rpx;
        padding-bottom: 20rpx;
        display: flex;
        justify-content: left;
        button {
            width: 190rpx;
            margin: 0;
            padding-left: 80rpx;
            height: 70rpx;
            line-height: 70rpx;
            font-size: 32rpx;
            text-align: center;
            background: #0256FF;
            .back-icon {
                position: absolute;
                left: 30rpx;
            }
        }
    }
}
</style>
