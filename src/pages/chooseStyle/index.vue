<template>
    <view class="page choose-cp">
        <view class="choose-cp__content">
            <view class="common-table">
                <view class="common-table__header">
                    <view class="th-item selector">选择</view>
                    <view class="th-item flex">裁片名</view>
                    <view class="th-item quantity">数量</view>
                    <view class="th-item roate">旋转</view>
                    <view class="th-item angle">倾斜</view>
                    <view class="th-item line">布纹线</view>
                    <view class="th-item limit">上限</view>
                </view>
                <view class="common-table__body">
                    <view class="row-item"
                        v-for="(item, index) in tableData"
                        @click="handlePreview(item)"
                        :key="index"
                        :class="{'light-bg': index%2, 'active': activeRowId === item.id}"
                    >
                        <view class="row-item__column selector">
                            <checkbox class="selector-checkbox"
                                :value="item.id"
                                :checked="item.usedFlag"
                                color="#127FD2"
                                @click="handleSelect(item)"
                            />
                        </view>
                        <view class="row-item__column flex">
                            <view class="info-name">{{ item.name || '' }}</view>
                            <view class="info-detail area-info">
                                <text>{{ !item.area ? 0 : item.area.toFixed(2) }} ft²</text><text>{{ item.heterogeneity
                                    === '1' ? '异形' : '' }}</text>
                            </view>
                        </view>
                        <view class="row-item__column quantity">{{ item.quantity }}</view>
                        <view class="row-item__column roate">{{ item.rotationRestriction || '-' }}</view>
                        <view class="row-item__column angle">{{ item.tiltAngle || '-' }}</view>
                        <view class="row-item__column line">{{ !item.textureLineAngle ? 0 :
                            item.textureLineAngle.toFixed(2) }}</view>
                        <view class="row-item__column limit">{{ item.singlePlMax || '-' }}</view>
                    </view>
                    <view class="no-data" v-if="!tableData.length">
                        暂无数据
                    </view>
                </view>
            </view>
        </view>
        <view class="choose-cp__bottom">
            <view class="mini-preview" v-show="showPreview">
                <uni-icons @click="closePreview" class="info-icon" type="close" color="#000" size="30"></uni-icons>
                <image class="preview-img" :src="svgUrl" />
            </view>
            <view class="opt-row">
                <view class="opt-left">
                    <checkbox class="selector-checkbox"
                        :value="true"
                        :checked="selectAll"
                        color="#127FD2"
                        @click="handleSelectAll"
                    ></checkbox>
                    <text class="all-text">全选</text>
                </view>
                <text class="view-svg" @click="handleToViewSvg">查看款式图</text>

                <view class="select-info">
                    <view>已选择：{{ tableData.filter(a => a.usedFlag).length }}</view>
                    <view>总面积：{{ totalArea }}</view>
                </view>
                <button type="primary" :disabled="submitBtnDisabled" class="submit-btn" @click="saveSelect">
                    确认选择
                </button>
            </view>
        </view>
    </view>
</template>

<script>
import { getStyleCpList, saveSelectedCpData } from 'api/order';
import { mapState, mapMutations } from 'vuex';

export default {
    data() {
        return {
            loading: false,
            tableData: [],
            orderId: '',
            styleId: '',
            styleName: '',
            showPreview: false,
            selectAll: false,
            previewSvgPath: '',
            submitBtnDisabled: false,
            activeRowId: '',
            svgUrl: '',
        };
    },
    computed: {
        ...mapState(['cachedSelectedCpIds']),
        totalArea() {
            return this.tableData.filter(a => a.usedFlag).reduce((total, item) => {
                return total + item.area;
            }, 0).toFixed(3);
        },
    },
    onLoad(query) {
        this.orderId = query.orderId;
        this.styleId = query.id;
        this.styleName = decodeURIComponent(query.styleName);
        if (!this.orderId || !this.styleId) {
            return;
        }
        uni.setNavigationBarTitle({
            title: this.styleName,
        });
    },

    async onShow() {
        this.svgUrl =  '';
        this.activeRowId = '';
        this.showPreview = false;
        this.submitBtnDisabled = false;
        await this.getCpData();
    },
    methods: {
        ...mapMutations(['setCacheSelectedCpIds']),
        // renderImgSvg(url) {
        //     const that = this;
        //     wx.request({
        //         url, // 你的接口地址
        //         method: 'GET', // 请求方式，默认为GET
        //         header: {
        //             'content-type': 'application/json', // 默认值
        //         },
        //         success: function(res) {
        //             // 请求成功的回调函数
        //             const base64 = wx.arrayBufferToBase64(
        //                 new Uint8Array(unescape(encodeURIComponent(res.data)).split('').map(c => c.charCodeAt(0))),
        //             );
        //             const dataURL = `data:image/svg+xml;base64,${base64}`;
        //             that.svgBase64 = dataURL;
        //             // that.svgUrl = res.data.replace(/<svg/, `<svg width="${80}" height="${80}"`);
        //         },
        //         fail: function(err) {
        //             // 请求失败的回调函数
        //             console.error('数据获取失败:', err);
        //         },
        //     });
        // },
        handlePreview(item) {
            this.activeRowId = item.id;
            if (!item.svgPath) {
                return;
            }
            this.svgUrl = item.svgPath;
            this.showPreview = true;
        },
        handleToViewSvg() {
            console.log('handleToViewSvg = ', this.loading);
            if (this.loading) {
                return;
            }
            this.loading = true;
            this.setCacheSelectedCpIds(this.tableData.filter(a => a.usedFlag).map(a => a.id));
            uni.navigateTo({
                url:
                    `pages/viewSvg/index?pageName=${(this.styleName)}&styleId=${this.styleId}`,
            });
        },
        handleSelectAll() {
            this.submitBtnDisabled = false;
            this.selectAll = !this.selectAll;
            this.tableData.forEach(a => {
                a.usedFlag = this.selectAll;
            });
        },
        closePreview() {
            this.showPreview = false;
        },
        handleSelect(item) {
            this.submitBtnDisabled = false;
            item.usedFlag = !item.usedFlag;
            if (this.tableData.every(a => a.usedFlag)) {
                this.selectAll = true;
            } else {
                this.selectAll = false;
            }
        },
        getCpData() {
            this.loading = true;
            const that = this;
            uni.showLoading({
                title: '加载中...',
                mask: true,
            });
            getStyleCpList({
                cpId: this.styleId,
                orderId: this.orderId,
            }).then(res => {
                const { code, data } = res.data;
                if (code === 0) {
                    const { total, records = [] } = data;
                    that.tableData = records;
                    that.selectAll = true;
                    if (that.cachedSelectedCpIds.length > 0) {
                        that.tableData.forEach(a => {
                            a.usedFlag = this.cachedSelectedCpIds.includes(a.id);
                        });
                        this.$nextTick(() => {
                            this.setCacheSelectedCpIds([]);
                        });
                    }
                    that.selectAll = that.tableData.every(a => a.usedFlag);
                    that.total = total;
                }
            }).finally(() => {
                uni.hideLoading();
                this.loading = false;
                this.$nextTick(() => {
                    this.setCacheSelectedCpIds([]);
                });
            });
        },
        saveSelect() {
            console.log('saveSelect = ', this.loading);
            if (this.loading || this.submitBtnDisabled) {
                return;
            }

            this.submitBtnDisabled = true;
            uni.showLoading({
                title: '保存中...',
                mask: true,
            });
            const params = {
                orderId: this.orderId,
                cpId: this.styleId,
                blockIds: this.tableData.filter(a => a.usedFlag).map(a => a.id),
            };
            saveSelectedCpData(params).then(res => {
                const { code } = res.data;
                this.loading = false;
                uni.hideLoading();
                if (code === 0) {
                    this.$toast.success('设置成功');
                    uni.navigateBack({
                        url: '/pages/addStyle/index?orderId=' + this.orderId,
                    });
                } else {
                    this.submitBtnDisabled = false;
                    this.$toast.error('设置失败');
                }
            }).catch(() => {
                this.submitBtnDisabled = false;
                uni.hideLoading();
                this.$toast.error('设置失败');
            });
        },
    },
};
</script>

<style lang="scss">
.choose-cp {
    height: 100vh;
    background: #fff;
    &__content {
        height: calc(100% - 160rpx);
        .common-table {
            .area-info {
                display: flex;
                justify-content: space-between;
                font-weight: bold;
            }
            .row-item__column {
                font-size: 24rpx;
            }
            .quantity {
                width: 70rpx;
            }
            .roate,.angle, .limit {
                width: 80rpx;
            }
            .line {
                width: 90rpx;
            }
            .row-item.active {
                border: 5px solid #02A7F0;
                .row-item__column:not(.flex) {
                    line-height: 80rpx;
                }
                .info-name {
                    padding-top: 5rpx;
                }
                .selector {
                    width: 70rpx;
                    border: none;
                    .selector-checkbox {
                        margin-left: -3px;
                    }
                }
                .limit{
                    width: 70rpx;
                    padding-left: 5px;
                }
            }
        }

    }
    &__bottom {
        width: 100vw;
        height: 160rpx;
        position: absolute;
        bottom: 0;
        background: #000;
        padding-bottom: 10rpx;
        .opt-row {
            padding: 0 20rpx;
            width: 100%;
            height: 160rpx;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: #fff;
            font-size: 28rpx;
            box-sizing: border-box;
            .opt-left {
                display: flex;
                align-items: center;
            }
            .view-svg {
                font-size: 28rpx;
                color: #02A7F0;
                text-decoration: underline;
            }
            button {
                width: 190rpx;
                height: 70rpx;
                font-size: 32rpx;
                line-height: 70rpx;
                margin: 0;
                background:#0256FF;
            }
        }
        .mini-preview {
            position: absolute;
            right: 0;
            background: #000;
            bottom: 144rpx;
            padding: 8rpx 12rpx;
            width: 340rpx;
            height: 290rpx;
            .preview-img {
                width: 100%;
                height: 100%;
                background: #fff;
            }
            .info-icon {
                position: absolute;
                right: 10rpx;
                top: -60rpx;
            }
        }
    }
}
</style>
