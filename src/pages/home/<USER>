<template>
    <view class="page home-page">
        <view class="home-page__header">
            <image class="logo" src="/static/logo.svg"></image>
            <view class="title">数智云脑</view>
        </view>

        <!-- 数据加载状态 -->
        <view v-if="dataLoading" class="loading-container">
            <text>数据加载中...</text>
        </view>

        <!-- 数据展示区域 -->
        <view v-else class="home-page__summary">
            <view class="summary-item" v-for="(summary, index) in summaryList" :key="index">
                <view class="summary-item__num">
                    {{ getSafeValue(summary.totalKey) || '0' }}
                </view>
                <view class="summary-item__name">
                    {{ summary.name }}
                    <text v-if="index > 1" class="summary-item__current">今日</text>
                </view>
                <view v-if="index > 1">
                    <text class="summary-item__detail">
                        成功: {{ getSafeValue(summary.successKey) || '0' }}
                    </text>
                    <text class="summary-item__detail">
                        失败: {{ getSafeValue(summary.failKey) || '0' }}
                    </text>
                </view>
            </view>
        </view>

        <view class="home-page__func">
            <h3>常用功能</h3>
            <view class="func-container">
                <view class="func-container__item"
                    v-for="(item) in funcList"
                    :key="item.text"
                    @click="handleMenuJump(item)"
                >
                    <image class="icon" :src="item.imgSrc"></image>
                    <view>{{ item.text }}</view>
                </view>
            </view>
        </view>

        <BottomTab :current-index="0"></BottomTab>
    </view>
</template>

<script>
import BottomTab from 'components/bottomTab/index.vue';
import { getCalcInfo } from 'api';

export default {
    components: {
        BottomTab,
    },
    data() {
        return {
            dataLoading: false,
            isLogin: false,
            summaryInfo: {
                cpFailOrderQuantity: '0',
                cpOrderQuantity: '0',
                cpSuccessOrderQuantity: '0',
                failOrderQuantity: '0',
                orderQuantity: '0',
                successOrderQuantity: '0',
                totalCpQuantity: '0',
                totalPlQuantity: '0',
            },
            summaryList: [{
                totalKey: 'totalPlQuantity',
                name: '总皮料数',
            }, {
                totalKey: 'totalCpQuantity',
                name: '款式裁片(套)',
            }, {
                totalKey: 'orderQuantity',
                name: '排料任务数',
                failKey: 'failOrderQuantity',
                successKey: 'successOrderQuantity',
            }, {
                totalKey: 'cpOrderQuantity',
                name: '裁切任务数',
                failKey: 'cpFailOrderQuantity',
                successKey: 'cpSuccessOrderQuantity',
            }],
            funcList: [{
                imgSrc: '/static/fun-icon-1.svg',
                text: '试排预测',
                url: '/pages/config/index',
            }, {
                imgSrc: '/static/fun-icon-2.svg',
                text: '真皮数字化',
            }, {
                imgSrc: '/static/fun-icon-3.svg',
                text: '产能交易',
            }, {
                imgSrc: '/static/fun-icon-4.svg',
                text: '皮料库存',
            }, {
                imgSrc: '/static/fun-icon-5.svg',
                text: '3D展品',
            }, {
                imgSrc: '/static/fun-icon-6.svg',
                text: '成本计算器',
            }, {
                imgSrc: '/static/fun-icon-7.svg',
                text: '数字资产',
            }],
        };
    },
    async onLoad() {
        try {
            await this.getUserInfo();
            this.getSummaryData();
        } catch (error) {
            uni.showToast({
                title: '页面加载失败',
                icon: 'error',
            });
        }
    },
    methods: {
        // 安全获取数据值
        getSafeValue(key) {
            if (!key || !this.summaryInfo) {
                return '0';
            }
            const value = this.summaryInfo[key];
            return value !== null && value !== undefined ? value.toString() : '0';
        },
        // 检查登录状态
        getUserInfo() {
            try {
                const token = uni.getStorageSync('accessToken');
                if (token) {
                    this.isLogin = true;
                } else {
                    this.isLogin = false;
                }
            } catch (error) {
                this.isLogin = false;
            }
        },

        handleMenuJump(item) {
            if (!this.isLogin) {
                uni.showToast({
                    title: '请先登录',
                    icon: 'none',
                }).then(() => {
                    uni.navigateTo({
                        url: '/pages/login/index',
                    });
                });
                return;
            }
            if (!item.url) {
                uni.showToast({
                    title: '正在筹备中，敬请期待',
                    icon: 'none',
                });
                return;
            }
            uni.navigateTo({
                url: item.url,
            });
        },

        getSummaryData() {
            this.dataLoading = true;
            uni.showLoading({
                title: '加载中...',
                mask: true,
            });

            // 添加错误处理和默认值
            getCalcInfo().then(res => {
                console.log('API响应:', res);
                if (res && res.data && res.data.data) {
                    this.summaryInfo = {
                        ...this.summaryInfo,  // 保持默认值
                        ...res.data.data,     // 覆盖API数据
                    };
                    console.log('更新后的数据:', this.summaryInfo);
                } else {
                    console.warn('API返回数据格式异常:', res);
                    // 保持默认值
                }
            }).catch(error => {
                console.error('获取数据失败:', error);
                uni.showToast({
                    title: '数据加载失败',
                    icon: 'error',
                });
                // 保持默认值
            }).finally(() => {
                uni.hideLoading();
                this.dataLoading = false;
            });
        },
    },
};
</script>

<style lang="scss">
.home-page {
    height: calc(100% - 70px);
    padding: 30rpx 40rpx;

    &__header {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;
        .logo {
            display: inline-block;
            width: 64rpx;
            height: 64rpx;
        }
        .title {
            display: inline-block;
            color: #fff;
            font-size: 40rpx;
        }
    }

    .loading-container {
        height: 400rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 32rpx;
    }

    &__summary {
        height: 400rpx;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        padding: 40rpx 60rpx;
        color: #fff;
        border-radius: 30rpx;
        background: #02A7F0;
        box-sizing: border-box;

        .summary-item {
            width: 58%;
            height: 140rpx;
            box-sizing: border-box;

            &__num {
                font-size: 72rpx;
                font-weight: 700;
                line-height: 70rpx;
            }

            &__name {
                font-size: 28rpx;
            }

            &__current {
                font-size: 20rpx;
                background: #000000;
                border-radius: 4rpx;
                margin-left: 8rpx;
            }

            &__detail {
                font-size: 24rpx;
                &:first-child {
                    padding-right: 8rpx;
                }
            }

            &:nth-child(even) {
                width: 42%;
            }
        }
    }

    &__func {
        margin-top: 20rpx;

        h3 {
            color: #fff;
            line-height: 60rpx;
        }

        .func-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;

            &__item {
                width: 30%;
                height: 220rpx;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                background: #fff;
                border-radius: 16rpx;
                margin-right: 20rpx;
                margin-bottom: 20rpx;

                .icon {
                    width: 100rpx;
                    height: 100rpx;
                }

                view {
                    color: #434343;
                    font-size: 32rpx;
                    margin-top: 10rpx;
                }

                &:nth-child(3n) {
                    margin-right: 0;
                }
            }
        }
    }
}
</style>
