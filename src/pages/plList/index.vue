<template>
    <view class="page pl-list">
        <view class="pl-list__filter">
            <view class="task-type-select">
                <picker @change="taskTypePickerChange"
                    :value="taskTypeIndex"
                    mode="selector"
                    range-key="label"
                    class="picker-item"
                    :range="taskTypeOptions"
                >
                    <view class="uni-input">{{ taskTypeOptions[taskTypeIndex].label }}</view>
                </picker>
                <uni-icons class="picker-icon" type="down" color="#fff" size="20"></uni-icons>
            </view>
            <view class="input-filter">
                <uni-icons
                    style="margin-right: 10px;"
                    class="picker-icon"
                    type="loop"
                    color="#fff"
                    size="30"
                    @click="reloadList"
                ></uni-icons>
                <uni-icons class="picker-icon" type="search" color="#fff" size="30"></uni-icons>
            </view>
        </view>
        <view class="pl-list__content">
            <view class="common-table">
                <view class="common-table__header">
                    <view class="th-item order">编号</view>
                    <view class="th-item flex">任务信息</view>
                    <view class="th-item status">状态</view>
                </view>
                <view class="common-table__body">
                    <uni-swipe-action ref="swipeGroup" class="swipe-row" :auto-close="true">
                        <uni-swipe-action-item
                            v-for="(item, index) in tableData"
                            :key="index"
                            class="swipe-item"
                            @click="optionClick(item, $event)"
                            :right-options="statusMap[item.status].rightOptions"
                        >
                            <view class="row-item" :class="{'light-bg': index%2}">
                                <text class="row-item__column order">{{ index + 1 }}</text>
                                <view class="row-item__column flex">
                                    <view class="info-name">{{ item.orderName }}</view>
                                    <view class="info-detail">
                                        <text class="info-detail-item">
                                            <text class="title">皮料数：</text>
                                            <text class="num">{{ item.plQuantity || 0 }}</text>
                                        </text>
                                        <text class="info-detail-item">
                                            <text class="title">裁片数：</text>
                                            <text class="num">{{ item.cpQuantity || 0 }}</text>
                                        </text>
                                    </view>
                                </view>
                                <text class="row-item__column status"
                                    :style="{color: statusMap[item.status].color}"
                                    @click="toDetail(item)"
                                >
                                    {{ statusMap[item.status].text }}
                                </text>
                            </view>
                        </uni-swipe-action-item>
                    </uni-swipe-action>
                    <!--                    <view class="no-data" v-if="!tableData.length">-->
                    <!--                        暂无数据-->
                    <!--                    </view>-->
                    <uni-load-more :status="moreStatus" :content-text="contentText" @clickLoadMore="handleLoadMore" />
                </view>
            </view>
            <!--            <uni-pagination-->
            <!--                v-show="!loading && tableData.length"-->
            <!--                class="common-table-pagination"-->
            <!--                :total="total"-->
            <!--                :current="currentPage"-->
            <!--                :pageSize="pageSize"-->
            <!--                @change="handleChangePage"-->
            <!--            />-->
        </view>
        <BottomTab :current-index="2"></BottomTab>
    </view>
</template>

<script>
import BottomTab from 'components/bottomTab/index.vue';
import { getOrderList } from 'api';
import { operateOrder, deleteOrder, returnWarehouse, copyOrder } from 'api/order';

export default {
    components: { BottomTab },
    data() {
        return {
            loading: false,
            moreStatus: '',
            rightOptions: [{
                text: '删除',
                style: {
                    backgroundColor: '#F56C6C',
                },
            }],
            contentText: {
                contentdown: '查看更多',
                contentrefresh: '加载中...',
                contentnomore: '没有更多',
            },
            statusMap: {
                '1': {
                    text: '进行中',
                    color: 'blue',
                    rightOptions: [{
                        text: '终止',
                        type: 'shut',
                        style: {
                            backgroundColor: '#FF0000',
                        },
                    }],
                },
                '2': {
                    text: '排队中',
                    color: '#333',
                    rightOptions: [{
                        text: '取消',
                        type: 'cancel',
                        style: {
                            backgroundColor: '#000',
                        },
                    }],
                },
                '3': {
                    text: '已完成',
                    color: 'green',
                    rightOptions: [{
                        text: '查看',
                        type: 'view',
                        style: {
                            backgroundColor: '#000',
                        },
                    }, {
                        text: '皮料回库',
                        type: 'recycle',
                        style: {
                            backgroundColor: '#000',
                        },
                    }, {
                        text: '复制',
                        type: 'copy',
                        style: {
                            backgroundColor: '#000',
                        },
                    }, {
                        text: '删除',
                        type: 'delete',
                        style: {
                            backgroundColor: '#FF0000',
                        },
                    }],
                },
                '4': {
                    text: '已取消',
                    color: '#333',
                    rightOptions: [{
                        text: '编辑',
                        type: 'edit',
                        style: {
                            backgroundColor: '#000',
                        },
                    }, {
                        text: '裁片',
                        type: 'cp',
                        style: {
                            backgroundColor: '#000',
                        },
                    }, {
                        text: '皮料',
                        type: 'pl',
                        style: {
                            backgroundColor: '#000',
                        },
                    }, {
                        text: '删除',
                        type: 'delete',
                        style: {
                            backgroundColor: '#FF0000',
                        },
                    }],
                },
                '5': {
                    text: '未发布',
                    color: '#333',
                    rightOptions: [{
                        text: '编辑',
                        type: 'edit',
                        style: {
                            backgroundColor: '#000',
                        },
                    }, {
                        text: '裁片',
                        type: 'cp',
                        style: {
                            backgroundColor: '#000',
                        },
                    }, {
                        text: '皮料',
                        type: 'pl',
                        style: {
                            backgroundColor: '#000',
                        },
                    }, {
                        text: '删除',
                        type: 'delete',
                        style: {
                            backgroundColor: '#FF0000',
                        },
                    }],
                },
                '6': {
                    text: '失败',
                    color: 'red',
                    rightOptions: [{
                        text: '编辑',
                        type: 'edit',
                        style: {
                            backgroundColor: '#000',
                        },
                    }, {
                        text: '裁片',
                        type: 'cp',
                        style: {
                            backgroundColor: '#000',
                        },
                    }, {
                        text: '皮料',
                        type: 'pl',
                        style: {
                            backgroundColor: '#000',
                        },
                    }, {
                        text: '删除',
                        type: 'delete',
                        style: {
                            backgroundColor: '#FF0000',
                        },
                    }],
                },
            },
            tableData: [],
            taskTypeIndex: 0,
            taskTypeOptions: [
                {
                    label: '全部',
                    value: '',
                }, {
                    label: '未发布',
                    value: '5',
                }, {
                    label: '进行中',
                    value: '1',
                }, {
                    label: '排队中',
                    value: '2',
                }, {
                    label: '已取消',
                    value: '4',
                }, {
                    label: '失败',
                    value: '6',
                }, {
                    label: '已完成',
                    value: '3',
                },
            ],
            currentPage: 1,
            pageSize: 10,
            total: 0,
        };
    },
    onShow() {
        this.currentPage = 1;
        this.taskTypeIndex = 0;
        this.tableData = [];
        this.moreStatus = '';
        this.getPlList();
    },
    methods: {
        closeAllSwipes() {
            try {
                if (this.$refs.swipeGroup && this.$refs.swipeGroup.closeAll) {
                    this.$refs.swipeGroup.closeAll();
                }
            } catch (e) {
                // noop
            }
        },
        toDetail(item) {
            // if (item.status !== 3) {
            //     return;
            // }
            uni.navigateTo({
                url: `/pages/plDetail/index?id=${item.id}`,
            });
        },
        handleLoadMore() {
            if (this.moreStatus === 'nomore') {
                return;
            }
            this.currentPage++;
            this.getPlList();
        },
        handleChangePage(e) {
            console.log('handleChangePage = ', e, this.currentPage);
            if (e.type === 'next') {
                this.currentPage = e.current;
                this.getPlList();
            }
        },
        optionClick(item, e) {
            const optionsType = this.statusMap[item.status].rightOptions[e.index].type;
            const actionMap = {
                shut: () => this.confirmAndRun('终止任务', '确认终止该任务吗？', async() => await this.terminateOrder(item)),
                cancel: () => this.confirmAndRun('取消任务', '确认取消该任务吗？', async() => await this.cancelOrder(item)),
                delete: () => this.confirmAndRun('删除任务', '确认删除该任务吗？', async() => await this.deleteOrder(item)),
                recycle: () => this.confirmAndRun('皮料回库', '确认将该任务的皮料回库吗？', async() => await this.recycleLeather(item)),
                view: () => this.toDetail(item),
                copy: () => this.copyOrder(item),
                edit: () => this.editOrder(item),
                pl: () => this.manageLeather(item),
                cp: () => this.managePieces(item),
            };

            const handler = actionMap[optionsType];
            if (handler) {
                handler();
                this.closeAllSwipes();
            } else {
                console.warn('Unknown option type: ', optionsType);
            }
        },
        async confirmAndRun(title, content, runFn) {
            const res = await new Promise(resolve => {
                uni.showModal({
                    title,
                    content,
                    success: (result) => resolve(result),
                });
            });
            if (!res.confirm) {
                return;
            }
            try {
                uni.showLoading({ title: '请稍候...', mask: true });
                const { data } = await runFn();
                uni.hideLoading();
                if (data?.code !== 0) {
                    throw new Error(data?.message);
                }
                uni.showToast({ title: '操作成功', icon: 'success' });
                setTimeout(() => {
                    this.reloadList();
                }, 1500);
            } catch (error) {
                uni.hideLoading();
                uni.showToast({ title: error?.message || '操作失败', icon: 'none' });
            }
        },
        // 以下为占位的业务动作，后续由具体 API 实现替换
        async terminateOrder(item) {
            return await operateOrder({ id: item.id, action: 2 });
        },
        async cancelOrder(item) {
            return await operateOrder({ id: item.id, action: 1 });
        },
        async deleteOrder(item) {
            return await deleteOrder(item.id);
        },
        async recycleLeather(item) {
            return await returnWarehouse(item.id);
        },
        async copyOrder(item) {
            try {
                uni.showLoading({ title: '请稍候...', mask: true });
                const { data } = await copyOrder(item.id);
                // 期望后端返回新纪录对象 data.data
                const newRecord = data?.data;
                if (data?.code === 0 && newRecord) {
                    // 将新数据插入到当前行后面
                    const idx = this.tableData.findIndex(r => r.id === item.id);
                    if (idx >= 0) {
                        this.tableData.splice(idx + 1, 0, newRecord);
                    } else {
                        this.tableData.unshift(newRecord);
                    }
                    uni.showToast({ title: '复制成功', icon: 'success' });
                } else {
                    uni.showToast({ title: '复制失败', icon: 'none' });
                }
            } catch (error) {
                uni.showToast({ title: error?.message || '复制失败', icon: 'none' });
            } finally {
                uni.hideLoading();
            }
        },
        editOrder(item) {
            // 待确认具体编辑页，先占位跳转
            uni.navigateTo({ url: `/pages/config/index?orderId=${item.id}&readonly=${item.status === 5 ? '' : 'true'}` });
        },
        manageLeather(item) {
            // 皮料管理 - 跳转到config页面皮料tab
            uni.navigateTo({ url: `/pages/config/index?orderId=${item.id}&activeTab=2&readonly=${item.status === 5 ? '' : 'true'}` });
        },
        managePieces(item) {
            // 裁片管理 - 跳转到config页面裁片tab
            uni.navigateTo({ url: `/pages/config/index?orderId=${item.id}&activeTab=1&readonly=${item.status === 5 ? '' : 'true'}` });
        },
        async callApiPlaceholder(apiName, payload) {
            // 预留统一 API 调用占位，后续替换为实际接口，如: return api[apiName](payload)
            // 这里直接返回成功 Promise，避免打断现有流程
            return Promise.resolve({ success: true, api: apiName, payload });
        },
        taskTypePickerChange(e) {
            this.taskTypeIndex = e.detail.value;
            this.reloadList();
        },
        reloadList() {
            this.currentPage = 1;
            this.tableData = [];
            this.getPlList();
        },
        getPlList() {
            this.loading = true;
            uni.showLoading({
                title: '加载中...',
                mask: true,
            });
            const searchParams = {
                status: this.taskTypeOptions[this.taskTypeIndex].value,
                current: this.currentPage,
                size: this.pageSize,
            };
            getOrderList(searchParams).then(res => {
                const { total, records = [] } = res.data?.data || {};
                this.tableData = this.tableData.concat(records);
                this.total = total;
                if (this.total > this.tableData.length) {
                    this.moreStatus = 'more';
                } else {
                    this.moreStatus = 'nomore';
                }
            }).finally(() => {
                uni.hideLoading();
                this.loading = false;
            });
        },
    },
};
</script>

<style lang="scss">
.pl-list {
    height: calc(100% - 70px);
    background: #fff;
    &__filter {
        height: 120rpx;
        background: #000;
        padding: 10rpx 40rpx;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .task-type-select {
            width: 220rpx;
            height: 60rpx;
            padding: 0 30rpx 0 15rpx;
            line-height: 60rpx;
            border: 1px solid #f9f9f9;
            text-align: center;
            position: relative;
            .picker-item {
                color: #fff;
                font-size: 14px;
            }

            .picker-icon {
                position: absolute;
                right: 8rpx;
                top: 2rpx;
            }
        }
    }
    &__content {
        height: calc(100% - 130rpx);
        overflow: hidden;
        .status {
            width: 180rpx;
        }
        .common-table {
            //height: calc(100% - 90rpx);
            .swipe-item {
                ::v-deep .uni-swipe_button {
                    padding: 0 20rpx;
                }
            }
        }
        .common-table-pagination .uni-pagination {
            padding: 0 20rpx;
        }
    }
}
</style>
