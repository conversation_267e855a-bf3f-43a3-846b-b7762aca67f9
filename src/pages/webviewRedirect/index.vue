<template>
    <Webview :url="url"></Webview>
</template>

<script>
import Webview from 'components/webview';

export default {
    components: { Webview },
    data() {
        return {
            url: '',
        };
    },
    onLoad() {
        const queryUrl = this.$Route.query.url;
        if (queryUrl) {
            this.url = decodeURIComponent(queryUrl);
            console.log('queryUrl', queryUrl, this.url);
        }
    },
};
</script>

<style>

</style>
