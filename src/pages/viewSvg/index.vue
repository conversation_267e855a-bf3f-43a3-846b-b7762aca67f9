<template>
    <view class="page view-style">
        <view class="view-style__content">
            <image class="svg-img" :src="infoData.svgPath"></image>
        </view>
        <view class="view-style__bottom">
            <view class="info-row">
                <text>各等级面积（ft²）</text>
                <text>总面积：{{ infoData.totalArea || 0 }}</text>
            </view>
            <view class="area-row">
                <text class="item">A: {{ infoData.levelAArea || 0 }}</text>
                <text class="item">B: {{ infoData.levelBArea || 0 }}</text>
                <text class="item">C: {{ infoData.levelCArea || 0 }}</text>
                <text class="item">D: {{ infoData.levelDArea || 0 }}</text>
                <text class="item">E: {{ infoData.levelEArea || 0 }}</text>
                <text class="item">其他: {{ infoData.otherArea || 0 }}</text>
            </view>
        </view>
    </view>
</template>

<script>

import { getLeatherPreviewInfo, getStylePreviewInfo } from 'api/order';

export default {
    data() {
        return {
            loading: false,
            pageName: '',
            svgSrc: '/static/logo.svg',
            sumNum: 57.1123,
            id: '',
            isStyle: false,
            infoData: {},
            baseUrl: this.$http.baseURL,
            svgBase64: '',
        };
    },
    onLoad(query) {
        this.pageName = decodeURIComponent(query.pageName);
        this.id = query.id;
        if (query.styleId) {
            this.id = query.styleId;
            this.isStyle = true;
        } else {
            this.id = query.plId;
        }

        if (!this.id) {
            return;
        }
        this.getData();
        uni.setNavigationBarTitle({
            title: decodeURIComponent(query.pageName),
        });
    },
    onShow() {
        this.svgBase64 = '';
    },
    methods: {
        renderImgSvg() {
            // console.log('ss =', url);
            // const that = this;
            // wx.request({
            //     url, // 你的接口地址
            //     method: 'GET', // 请求方式，默认为GET
            //     header: {
            //         'content-type': 'application/json', // 默认值
            //     },
            //     success: function(res) {
            //         // 请求成功的回调函数
            //         console.log('数据获取成功:', res.data);
            //         const base64 = wx.arrayBufferToBase64(
            //             new Uint8Array(unescape(encodeURIComponent(res.data)).split('').map(c => c.charCodeAt(0))),
            //         );
            //         const dataURL = `data:image/svg+xml;base64,${base64}`;
            //         that.svgBase64 = dataURL;
            //         // that.svgUrl = res.data.replace(/<svg/, `<svg width="${80}" height="${80}"`);
            //     },
            //     fail: function(err) {
            //         // 请求失败的回调函数
            //         console.error('数据获取失败:', err);
            //     },
            // });
        },
        getData() {
            this.loading = true;
            const apiFun = this.isStyle ? getStylePreviewInfo : getLeatherPreviewInfo;
            apiFun(this.id).then((res) => {
                const { code, data } = res.data;
                if (code === 0) {
                    this.infoData = data;
                    // this.renderImgSvg(this.isStyle ? `${this.baseUrl}${this.infoData.svgPath}`
                    //     :                        `${this.baseUrl}/plmaster/oss/${this.infoData.svgPath}?token=${uni.getStorageSync('accessToken')}`);
                    if (this.isStyle && this.infoData.cpLevelInfos) {
                        this.infoData.levelAArea = this.infoData.cpLevelInfos.find(a => a.level === 'A')?.value || 0;
                        this.infoData.levelBArea = this.infoData.cpLevelInfos.find(a => a.level === 'B')?.value || 0;
                        this.infoData.levelCArea = this.infoData.cpLevelInfos.find(a => a.level === 'C')?.value || 0;
                        this.infoData.levelDArea = this.infoData.cpLevelInfos.find(a => a.level === 'D')?.value || 0;
                        this.infoData.levelEArea = this.infoData.cpLevelInfos.find(a => a.level === 'E')?.value || 0;
                        this.infoData.otherArea = this.infoData.cpLevelInfos.find(a => a.level === '其他')?.value || 0;
                    }
                }
            }).finally(() => {
                this.loading =  false;
            });
        },
    },
};
</script>

<style lang="scss">
.view-style {
    height: 100vh;
    background: #fff;
    &__content {
        height: calc(100% - 200rpx);
        padding: 20rpx 30rpx;
        box-sizing: border-box;
        .svg-img {
            width: 100%;
            height: 100%;
        }
    }
    &__bottom {
        width: 100vw;
        height: 200rpx;
        position: absolute;
        bottom: 0;
        background: #000;
        font-size: 26rpx;
        color: #fff;
        padding: 20rpx 30rpx;
        box-sizing: border-box;
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 50rpx;
        }
        .area-row {
            .item {
                padding-right: 40rpx;
            }
        }
    }
}
</style>
