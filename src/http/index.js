var Fly = require('flyio/dist/npm/wx');
// #ifdef MP-ALIPAY
Fly = require('flyio/dist/npm/ap');
// #endif

var fly = new Fly();
import interceptor from './interceptors';
// fly.config.baseURL = 'http://plmaster.qianxunzj.com';
fly.config.baseURL = 'https://plmaster.qianxunzj.com';

if (process.env.NODE_ENV === 'production') {
    fly.config.baseURL = 'https://plmaster.qianxunzj.com';
}

fly.baseURL = fly.config.baseURL;
interceptor(fly);
export default fly;
