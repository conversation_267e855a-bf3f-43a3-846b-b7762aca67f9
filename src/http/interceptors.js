/**
 * 全局拦截器
 */
// 解决flyio 支付宝小程序ios端 PUT请求后端无法正常接收参数的问题
function handleAlipayPutRequest(request) {
    return new Promise((resolve, reject) => {
        const { baseURL, url, method, headers, body } = request;
        uni.request({
            url: baseURL + url,
            method: method,
            header: headers,
            data: body,
            success: (res) => {
                resolve(res);
            },
            fail: (err) => {
                reject(err);
            },
        });
    });
}

export default function interceptor(fly) {
    // http request 请求拦截器
    fly.interceptors.request.use((request, promise) => {
        // 给所有请求添加自定义header

        const accessToken = uni.getStorageSync('accessToken');
        if (accessToken) {
            request.headers.Authorization = accessToken;
        }
        // 时间戳
        request.method === 'GET' && (request.params._ = new Date().getTime());
        // #ifdef MP-ALIPAY
        if (request.method === 'PUT') {
            return promise.resolve(handleAlipayPutRequest(request));
        }
        // #endif
        return request;
    });

    // http response 拦截器
    fly.interceptors.response.use(
        (response) => {
            const data = response.data;
            try {
                if (response.data.code === 10063) {
                    uni.showToast({
                        title: '登录超时，请重新登录！',
                        icon: 'none',
                        duration: 2000,
                    });
                    setTimeout(() => {
                        const account = uni.getStorageSync('loginAccount') || '';
                        uni.navigateTo({
                            url: '/pages/login/index?account=' + account,
                        });
                    }, 2000);
                    return;
                } else if (response.data.code === 401) {
                    uni.showToast({
                        title: '登录信息已失效，请重新登录！',
                        icon: 'none',
                        duration: 2000,
                    });
                    setTimeout(() => {
                        const account = uni.getStorageSync('loginAccount') || '';
                        uni.navigateTo({
                            url: '/pages/login/index?account=' + account,
                        });
                    }, 2000);
                    return;
                } else {
                    response.data = JSON.parse(data);
                }
            } catch (err) {
                response.data = data;
            }
            return response;
        },
        (error) => {
            const originalRequest = error.request;
            const status = error.response && error.response.status;
            let data = error.response && error.response.data;
            try {
                data = JSON.parse(data);
            } catch (err) {
                console.log(err);
            }
            const message =  data && data.message;
            if (status === 401 && !['/plmaster/login', '/app/login'].includes(originalRequest.url)) { // 重新登录
                uni.navigateTo({
                    url: '/pages/login/index',
                });
            } else if (status >= 500) {
                uni.showToast({
                    title: '服务器开了点小差，请稍后再试',
                    icon: 'none',
                });
            } else {
                message && !originalRequest.noToast && uni.showToast({
                    title: message,
                    icon: 'none',
                });
            }
            return Promise.reject(error);
        },
    );
}
