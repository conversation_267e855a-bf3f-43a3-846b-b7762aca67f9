{
    "easycom": {
        "autoscan": true,
        "custom": {
            "^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"
        }
    },
    "pages": [ // pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/views
        {
            "path": "pages/home/<USER>",
            "name": "home",
            "meta": {
                "title": "数智云脑"
            },
            "style": {
                "navigationBarTitleText": "数智云脑"
            }
        },
        {
            "path": "pages/login/index",
            "name": "login",
            "meta": {
                "title": "数智云脑"
            },
            "style": {
                "navigationBarTitleText": "登录"
            }
        },
        {
            "path": "pages/plList/index",
            "name": "plList",
            "meta": {
                "title": "数智云脑"
            },
            "style": {
                "navigationBarTitleText": "排料任务管理"
            }
        },
        {
            "path": "pages/plDetail/index",
            "name": "plDetail",
            "meta": {
                "title": "数智云脑"
            },
            "style": {
                "navigationBarTitleText": "排料结果详情"
            }
        },
        {
            "path": "pages/config/index",
            "name": "config",
            "meta": {
                "title": "数智云脑"
            },
            "style": {
                "navigationBarTitleText": "试排任务"
            }
        },
        {
            "path": "pages/styleDetail/index",
            "name": "styleDetail",
            "meta": {
                "title": "数智云脑"
            },
            "style": {
                "navigationBarTitleText": "试排任务"
            }
        },
        {
            "path": "pages/addStyle/index",
            "name": "addStyle",
            "meta": {
                "title": "数智云脑"
            },
            "style": {
                "navigationBarTitleText": "筛选款式"
            }
        },
        {
            "path": "pages/addLeather/index",
            "name": "addLeather",
            "meta": {
                "title": "数智云脑"
            },
            "style": {
                "navigationBarTitleText": "筛选皮料批次"
            }
        },

        {
            "path": "pages/chooseLeather/index",
            "name": "chooseLeather",
            "meta": {
                "title": "数智云脑"
            },
            "style": {
                "navigationBarTitleText": "皮料选择"
            }
        },
        {
            "path": "pages/chooseStyle/index",
            "name": "chooseStyle",
            "meta": {
                "title": "数智云脑"
            },
            "style": {
                "navigationBarTitleText": "选择裁片"
            }
        },
        {
            "path": "pages/viewSvg/index",
            "name": "viewSvg",
            "meta": {
                "title": "数智云脑"
            },
            "style": {
                "navigationBarTitleText": "预览图"
            }
        },
        {
            "path": "pages/cut/index",
            "name": "cut",
            "meta": {
                "title": "数智云脑"
            },
            "style": {
                "navigationBarTitleText": "裁切"
            }
        },
        {
            "path": "pages/resource/index",
            "name": "resource",
            "meta": {
                "title": "数智云脑"
            },
            "style": {
                "navigationBarTitleText": "资源"
            }
        },
        {
            "path": "pages/account/index",
            "name": "account",
            "meta": {
                "title": "数智云脑"
            },
            "style": {
                "navigationBarTitleText": "个人信息"
            }
        },
        {
            "path": "pages/webviewRedirect/index",
            "name": "webviewRedirect",
            "meta": {
                "title": "数智云脑"
            },
            "style": {
                "navigationBarTitleText": "3D预览"
            }
        }
    ],
    "tabBar": {
        "custom": true,
        "color": "#fff",
        "selectedColor": "#127FD2",
        "list": [
            {
                "text": "数智云脑",
                "pagePath": "pages/home/<USER>"
            },
            {
                "text": "资源",
                "pagePath": "pages/resource/index"
            },
            {
                "text": "排料",
                "pagePath": "pages/plList/index"
            },
            {
                "text": "裁切",
                "pagePath": "pages/cut/index"
            },
            {
                "text": "我的",
                "pagePath": "pages/account/index"
            }
        ]
    },
    "globalStyle": {
        "navigationBarTextStyle": "white",
        "navigationBarTitleText": "上上签",
        "navigationBarBackgroundColor": "#000000",
        "backgroundColor": "#000"
    }
}
