import Vue from 'vue';

/**
 * @description: 账号密码登录
 */
export function login(data) {
    return Vue.$http.post('/plmaster/login', data);
}

/**
 * @description: 微信一键登录
 */
export function fastLogin(data) {
    return Vue.$http.post('/plmaster/app/login', data);
}

/**
 * @description: 获取用户信息
 */
export function getUserInfo(noToast) {
    return Vue.$http.get('/plmaster/app/user/get-user', { noToast });
}

/**
 * @description: 更新用户信息（昵称/邮箱/手机号/头像）
 * 接口: PUT /plmaster/user
 * Body: multipart/form-data { file?, email?, nickname?, phone?, id? }
 */
export function updateUser(data) {
    // 传入FormData，走multipart/form-data
    return Vue.$http.post('/plmaster/user/update', data);
}

/**
 * @description: 修改密码
 * 接口: POST /plmaster/user/change-password
 * Body: { originalPassword, password, confirmPassword }
 */
export function changePassword(data) {
    return Vue.$http.post('/plmaster/user/change-password', data);
}
