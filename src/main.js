import Vue from 'vue';
import App from './App.vue';
import $http from './http/index.js';
import store from './store/';
import toast from './common/toast';
import { router, RouterMount } from './router';

Vue.use(router);
Vue.config.productionTip = false;
Vue.$http = $http;
Vue.prototype.$http = $http;
Vue.$store = store;
Vue.prototype.$store = store;
Vue.prototype.$toast = toast;
Vue.$toast = toast;
Vue.config.errorHandler = function(error) {
    console.log(error); // 防止退出登录时控制台报错提示
};
import 'styles/index.scss';
import 'iconfont/iconfont.css';
const app = new Vue({
    store,
    ...App,
});

// #ifdef H5
// eslint-disable-next-line new-cap
RouterMount(app, router, '#app');
// #endif

// #ifndef H5
app.$mount(); // 为了兼容小程序及app端必须这样写才有效果
// #endif
