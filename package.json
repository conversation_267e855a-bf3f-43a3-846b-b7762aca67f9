{"name": "normal", "version": "0.1.0", "private": true, "scripts": {"build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch", "test:mp-weixin": "cross-env UNI_PLATFORM=mp-weixin jest -i", "dev:mp-alipay": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay vue-cli-service uni-build --watch", "build:mp-alipay": "cross-env UNI_PLATFORM=mp-alipay vue-cli-service uni-build", "lint": "eslint --ext .js,.vue src --max-warnings 0", "lint-fix": "eslint --fix --ext .js,.vue src"}, "dependencies": {"@dcloudio/uni-app": "^2.0.2-3081220230817001", "@dcloudio/uni-app-plus": "^2.0.2-3081220230817001", "@dcloudio/uni-h5": "^2.0.2-3081220230817001", "@dcloudio/uni-i18n": "^2.0.2-3081220230817001", "@dcloudio/uni-mp-360": "^2.0.2-3081220230817001", "@dcloudio/uni-mp-alipay": "^2.0.2-3081220230817001", "@dcloudio/uni-mp-baidu": "^2.0.2-3081220230817001", "@dcloudio/uni-mp-jd": "^2.0.2-3081220230817001", "@dcloudio/uni-mp-kuaishou": "^2.0.2-3081220230817001", "@dcloudio/uni-mp-lark": "^2.0.2-3081220230817001", "@dcloudio/uni-mp-qq": "^2.0.2-3081220230817001", "@dcloudio/uni-mp-toutiao": "^2.0.2-3081220230817001", "@dcloudio/uni-mp-vue": "^2.0.2-3081220230817001", "@dcloudio/uni-mp-weixin": "^2.0.2-3081220230817001", "@dcloudio/uni-mp-xhs": "^2.0.2-3081220230817001", "@dcloudio/uni-quickapp-native": "^2.0.2-3081220230817001", "@dcloudio/uni-quickapp-webview": "^2.0.2-3081220230817001", "@dcloudio/uni-stacktracey": "^2.0.2-3081220230817001", "@dcloudio/uni-stat": "^2.0.2-3081220230817001", "@dcloudio/uni-ui": "^1.4.26", "@vue/shared": "^3.0.0", "core-js": "^3.6.5", "crypto-js": "^4.1.1", "dayjs": "^1.11.10", "flyio": "^0.6.2", "lint-staged": "^10.5.4", "lodash": "^4.17.21", "sass": "^1.32.13", "sass-loader": "^10.1.1", "regenerator-runtime": "^0.12.1", "uni-read-pages": "^1.0.5", "uni-simple-router": "^2.0.7", "vue": "^2.6.11", "vuex": "^3.2.0"}, "devDependencies": {"@babel/eslint-parser": "^7.19.1", "@babel/runtime": "~7.12.0", "@dcloudio/types": "^3.3.2", "@dcloudio/uni-automator": "^2.0.2-3081220230817001", "@dcloudio/uni-cli-i18n": "^2.0.2-3081220230817001", "@dcloudio/uni-cli-shared": "^2.0.2-3081220230817001", "@dcloudio/uni-helper-json": "*", "@dcloudio/uni-migration": "^2.0.2-3081220230817001", "@dcloudio/uni-template-compiler": "^2.0.2-3081220230817001", "@dcloudio/vue-cli-plugin-hbuilderx": "^2.0.2-3081220230817001", "@dcloudio/vue-cli-plugin-uni": "^2.0.2-3081220230817001", "@dcloudio/vue-cli-plugin-uni-optimize": "^2.0.2-3081220230817001", "@dcloudio/webpack-uni-mp-loader": "^2.0.2-3081220230817001", "@dcloudio/webpack-uni-pages-loader": "^2.0.2-3081220230817001", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-plugin-import": "^1.11.0", "cross-env": "^7.0.2", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "husky": "^4.2.3", "jest": "^25.4.0", "mini-types": "*", "miniprogram-api-typings": "*", "postcss-comment": "^2.0.0", "vue-template-compiler": "^2.6.11"}, "browserslist": ["Android >= 4.4", "ios >= 9"], "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --ext .js,.vue --max-warnings 0"]}, "uni-app": {"scripts": {}}, "engines": {"node": ">=14.0.0 <17.0.0", "npm": ">=6.0.0"}}